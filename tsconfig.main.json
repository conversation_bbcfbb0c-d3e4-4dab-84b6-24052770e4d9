{"extends": "./tsconfig.json", "compilerOptions": {"target": "ES2022", "module": "commonjs", "lib": ["ES2022"], "outDir": "dist/main", "rootDir": "src", "noEmit": false, "skipLibCheck": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@main/*": ["src/main/*"], "@shared/*": ["src/shared/*"]}}, "include": ["src/main/**/*", "src/preload/**/*", "src/shared/**/*"], "exclude": ["src/renderer", "node_modules", "**/*.test.ts"]}