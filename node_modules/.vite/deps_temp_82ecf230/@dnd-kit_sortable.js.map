{"version": 3, "sources": ["../../@dnd-kit/sortable/src/utilities/arrayMove.ts", "../../@dnd-kit/sortable/src/utilities/arraySwap.ts", "../../@dnd-kit/sortable/src/utilities/getSortedRects.ts", "../../@dnd-kit/sortable/src/utilities/isValidIndex.ts", "../../@dnd-kit/sortable/src/utilities/itemsEqual.ts", "../../@dnd-kit/sortable/src/utilities/normalizeDisabled.ts", "../../@dnd-kit/sortable/src/strategies/horizontalListSorting.ts", "../../@dnd-kit/sortable/src/strategies/rectSorting.ts", "../../@dnd-kit/sortable/src/strategies/rectSwapping.ts", "../../@dnd-kit/sortable/src/strategies/verticalListSorting.ts", "../../@dnd-kit/sortable/src/components/SortableContext.tsx", "../../@dnd-kit/sortable/src/hooks/defaults.ts", "../../@dnd-kit/sortable/src/hooks/utilities/useDerivedTransform.ts", "../../@dnd-kit/sortable/src/hooks/useSortable.ts", "../../@dnd-kit/sortable/src/types/type-guard.ts", "../../@dnd-kit/sortable/src/sensors/keyboard/sortableKeyboardCoordinates.ts"], "sourcesContent": ["/**\n * Move an array item to a different position. Returns a new array with the item moved to the new position.\n */\nexport function arrayMove<T>(array: T[], from: number, to: number): T[] {\n  const newArray = array.slice();\n  newArray.splice(\n    to < 0 ? newArray.length + to : to,\n    0,\n    newArray.splice(from, 1)[0]\n  );\n\n  return newArray;\n}\n", "/**\n * Swap an array item to a different position. Returns a new array with the item swapped to the new position.\n */\nexport function arraySwap<T>(array: T[], from: number, to: number): T[] {\n  const newArray = array.slice();\n\n  newArray[from] = array[to];\n  newArray[to] = array[from];\n\n  return newArray;\n}\n", "import type {\n  ClientRect,\n  UniqueIdentifier,\n  UseDndContextReturnValue,\n} from '@dnd-kit/core';\n\nexport function getSortedRects(\n  items: UniqueIdentifier[],\n  rects: UseDndContextReturnValue['droppableRects']\n) {\n  return items.reduce<ClientRect[]>((accumulator, id, index) => {\n    const rect = rects.get(id);\n\n    if (rect) {\n      accumulator[index] = rect;\n    }\n\n    return accumulator;\n  }, Array(items.length));\n}\n", "export function isValidIndex(index: number | null): index is number {\n  return index !== null && index >= 0;\n}\n", "import type {UniqueIdentifier} from '@dnd-kit/core';\n\nexport function itemsEqual(a: UniqueIdentifier[], b: UniqueIdentifier[]) {\n  if (a === b) {\n    return true;\n  }\n\n  if (a.length !== b.length) {\n    return false;\n  }\n\n  for (let i = 0; i < a.length; i++) {\n    if (a[i] !== b[i]) {\n      return false;\n    }\n  }\n\n  return true;\n}\n", "import type {Disabled} from '../types';\n\nexport function normalizeDisabled(disabled: boolean | Disabled): Disabled {\n  if (typeof disabled === 'boolean') {\n    return {\n      draggable: disabled,\n      droppable: disabled,\n    };\n  }\n\n  return disabled;\n}\n", "import type {ClientRect} from '@dnd-kit/core';\nimport type {SortingStrategy} from '../types';\n\n// To-do: We should be calculating scale transformation\nconst defaultScale = {\n  scaleX: 1,\n  scaleY: 1,\n};\n\nexport const horizontalListSortingStrategy: SortingStrategy = ({\n  rects,\n  activeNodeRect: fallbackActiveRect,\n  activeIndex,\n  overIndex,\n  index,\n}) => {\n  const activeNodeRect = rects[activeIndex] ?? fallbackActiveRect;\n\n  if (!activeNodeRect) {\n    return null;\n  }\n\n  const itemGap = getItemGap(rects, index, activeIndex);\n\n  if (index === activeIndex) {\n    const newIndexRect = rects[overIndex];\n\n    if (!newIndexRect) {\n      return null;\n    }\n\n    return {\n      x:\n        activeIndex < overIndex\n          ? newIndexRect.left +\n            newIndexRect.width -\n            (activeNodeRect.left + activeNodeRect.width)\n          : newIndexRect.left - activeNodeRect.left,\n      y: 0,\n      ...defaultScale,\n    };\n  }\n\n  if (index > activeIndex && index <= overIndex) {\n    return {\n      x: -activeNodeRect.width - itemGap,\n      y: 0,\n      ...defaultScale,\n    };\n  }\n\n  if (index < activeIndex && index >= overIndex) {\n    return {\n      x: activeNodeRect.width + itemGap,\n      y: 0,\n      ...defaultScale,\n    };\n  }\n\n  return {\n    x: 0,\n    y: 0,\n    ...defaultScale,\n  };\n};\n\nfunction getItemGap(rects: ClientRect[], index: number, activeIndex: number) {\n  const currentRect: ClientRect | undefined = rects[index];\n  const previousRect: ClientRect | undefined = rects[index - 1];\n  const nextRect: ClientRect | undefined = rects[index + 1];\n\n  if (!currentRect || (!previousRect && !nextRect)) {\n    return 0;\n  }\n\n  if (activeIndex < index) {\n    return previousRect\n      ? currentRect.left - (previousRect.left + previousRect.width)\n      : nextRect.left - (currentRect.left + currentRect.width);\n  }\n\n  return nextRect\n    ? nextRect.left - (currentRect.left + currentRect.width)\n    : currentRect.left - (previousRect.left + previousRect.width);\n}\n", "import {arrayMove} from '../utilities';\nimport type {SortingStrategy} from '../types';\n\nexport const rectSortingStrategy: SortingStrategy = ({\n  rects,\n  activeIndex,\n  overIndex,\n  index,\n}) => {\n  const newRects = arrayMove(rects, overIndex, activeIndex);\n\n  const oldRect = rects[index];\n  const newRect = newRects[index];\n\n  if (!newRect || !oldRect) {\n    return null;\n  }\n\n  return {\n    x: newRect.left - oldRect.left,\n    y: newRect.top - oldRect.top,\n    scaleX: newRect.width / oldRect.width,\n    scaleY: newRect.height / oldRect.height,\n  };\n};\n", "import type {SortingStrategy} from '../types';\n\nexport const rectSwappingStrategy: SortingStrategy = ({\n  activeIndex,\n  index,\n  rects,\n  overIndex,\n}) => {\n  let oldRect;\n  let newRect;\n\n  if (index === activeIndex) {\n    oldRect = rects[index];\n    newRect = rects[overIndex];\n  }\n\n  if (index === overIndex) {\n    oldRect = rects[index];\n    newRect = rects[activeIndex];\n  }\n\n  if (!newRect || !oldRect) {\n    return null;\n  }\n\n  return {\n    x: newRect.left - oldRect.left,\n    y: newRect.top - oldRect.top,\n    scaleX: newRect.width / oldRect.width,\n    scaleY: newRect.height / oldRect.height,\n  };\n};\n", "import type {ClientRect} from '@dnd-kit/core';\nimport type {SortingStrategy} from '../types';\n\n// To-do: We should be calculating scale transformation\nconst defaultScale = {\n  scaleX: 1,\n  scaleY: 1,\n};\n\nexport const verticalListSortingStrategy: SortingStrategy = ({\n  activeIndex,\n  activeNodeRect: fallbackActiveRect,\n  index,\n  rects,\n  overIndex,\n}) => {\n  const activeNodeRect = rects[activeIndex] ?? fallbackActiveRect;\n\n  if (!activeNodeRect) {\n    return null;\n  }\n\n  if (index === activeIndex) {\n    const overIndexRect = rects[overIndex];\n\n    if (!overIndexRect) {\n      return null;\n    }\n\n    return {\n      x: 0,\n      y:\n        activeIndex < overIndex\n          ? overIndexRect.top +\n            overIndexRect.height -\n            (activeNodeRect.top + activeNodeRect.height)\n          : overIndexRect.top - activeNodeRect.top,\n      ...defaultScale,\n    };\n  }\n\n  const itemGap = getItemGap(rects, index, activeIndex);\n\n  if (index > activeIndex && index <= overIndex) {\n    return {\n      x: 0,\n      y: -activeNodeRect.height - itemGap,\n      ...defaultScale,\n    };\n  }\n\n  if (index < activeIndex && index >= overIndex) {\n    return {\n      x: 0,\n      y: activeNodeRect.height + itemGap,\n      ...defaultScale,\n    };\n  }\n\n  return {\n    x: 0,\n    y: 0,\n    ...defaultScale,\n  };\n};\n\nfunction getItemGap(\n  clientRects: ClientRect[],\n  index: number,\n  activeIndex: number\n) {\n  const currentRect: ClientRect | undefined = clientRects[index];\n  const previousRect: ClientRect | undefined = clientRects[index - 1];\n  const nextRect: ClientRect | undefined = clientRects[index + 1];\n\n  if (!currentRect) {\n    return 0;\n  }\n\n  if (activeIndex < index) {\n    return previousRect\n      ? currentRect.top - (previousRect.top + previousRect.height)\n      : nextRect\n      ? nextRect.top - (currentRect.top + currentRect.height)\n      : 0;\n  }\n\n  return nextRect\n    ? nextRect.top - (currentRect.top + currentRect.height)\n    : previousRect\n    ? currentRect.top - (previousRect.top + previousRect.height)\n    : 0;\n}\n", "import React, {useEffect, useMemo, useRef} from 'react';\nimport {useDndContext, ClientRect, UniqueIdentifier} from '@dnd-kit/core';\nimport {useIsomorphicLayoutEffect, useUniqueId} from '@dnd-kit/utilities';\n\nimport type {Disabled, SortingStrategy} from '../types';\nimport {getSortedRects, itemsEqual, normalizeDisabled} from '../utilities';\nimport {rectSortingStrategy} from '../strategies';\n\nexport interface Props {\n  children: React.ReactNode;\n  items: (UniqueIdentifier | {id: UniqueIdentifier})[];\n  strategy?: SortingStrategy;\n  id?: string;\n  disabled?: boolean | Disabled;\n}\n\nconst ID_PREFIX = 'Sortable';\n\ninterface ContextDescriptor {\n  activeIndex: number;\n  containerId: string;\n  disabled: Disabled;\n  disableTransforms: boolean;\n  items: UniqueIdentifier[];\n  overIndex: number;\n  useDragOverlay: boolean;\n  sortedRects: ClientRect[];\n  strategy: SortingStrategy;\n}\n\nexport const Context = React.createContext<ContextDescriptor>({\n  activeIndex: -1,\n  containerId: ID_PREFIX,\n  disableTransforms: false,\n  items: [],\n  overIndex: -1,\n  useDragOverlay: false,\n  sortedRects: [],\n  strategy: rectSortingStrategy,\n  disabled: {\n    draggable: false,\n    droppable: false,\n  },\n});\n\nexport function SortableContext({\n  children,\n  id,\n  items: userDefinedItems,\n  strategy = rectSortingStrategy,\n  disabled: disabledProp = false,\n}: Props) {\n  const {\n    active,\n    dragOverlay,\n    droppableRects,\n    over,\n    measureDroppableContainers,\n  } = useDndContext();\n  const containerId = useUniqueId(ID_PREFIX, id);\n  const useDragOverlay = Boolean(dragOverlay.rect !== null);\n  const items = useMemo<UniqueIdentifier[]>(\n    () =>\n      userDefinedItems.map((item) =>\n        typeof item === 'object' && 'id' in item ? item.id : item\n      ),\n    [userDefinedItems]\n  );\n  const isDragging = active != null;\n  const activeIndex = active ? items.indexOf(active.id) : -1;\n  const overIndex = over ? items.indexOf(over.id) : -1;\n  const previousItemsRef = useRef(items);\n  const itemsHaveChanged = !itemsEqual(items, previousItemsRef.current);\n  const disableTransforms =\n    (overIndex !== -1 && activeIndex === -1) || itemsHaveChanged;\n  const disabled = normalizeDisabled(disabledProp);\n\n  useIsomorphicLayoutEffect(() => {\n    if (itemsHaveChanged && isDragging) {\n      measureDroppableContainers(items);\n    }\n  }, [itemsHaveChanged, items, isDragging, measureDroppableContainers]);\n\n  useEffect(() => {\n    previousItemsRef.current = items;\n  }, [items]);\n\n  const contextValue = useMemo(\n    (): ContextDescriptor => ({\n      activeIndex,\n      containerId,\n      disabled,\n      disableTransforms,\n      items,\n      overIndex,\n      useDragOverlay,\n      sortedRects: getSortedRects(items, droppableRects),\n      strategy,\n    }),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n      activeIndex,\n      containerId,\n      disabled.draggable,\n      disabled.droppable,\n      disableTransforms,\n      items,\n      overIndex,\n      droppableRects,\n      useDragOverlay,\n      strategy,\n    ]\n  );\n\n  return <Context.Provider value={contextValue}>{children}</Context.Provider>;\n}\n", "import {CSS} from '@dnd-kit/utilities';\n\nimport {arrayMove} from '../utilities';\n\nimport type {\n  AnimateLayoutChanges,\n  NewIndexGetter,\n  SortableTransition,\n} from './types';\n\nexport const defaultNewIndexGetter: NewIndexGetter = ({\n  id,\n  items,\n  activeIndex,\n  overIndex,\n}) => arrayMove(items, activeIndex, overIndex).indexOf(id);\n\nexport const defaultAnimateLayoutChanges: AnimateLayoutChanges = ({\n  containerId,\n  isSorting,\n  wasDragging,\n  index,\n  items,\n  newIndex,\n  previousItems,\n  previousContainerId,\n  transition,\n}) => {\n  if (!transition || !wasDragging) {\n    return false;\n  }\n\n  if (previousItems !== items && index === newIndex) {\n    return false;\n  }\n\n  if (isSorting) {\n    return true;\n  }\n\n  return newIndex !== index && containerId === previousContainerId;\n};\n\nexport const defaultTransition: SortableTransition = {\n  duration: 200,\n  easing: 'ease',\n};\n\nexport const transitionProperty = 'transform';\n\nexport const disabledTransition = CSS.Transition.toString({\n  property: transitionProperty,\n  duration: 0,\n  easing: 'linear',\n});\n\nexport const defaultAttributes = {\n  roleDescription: 'sortable',\n};\n", "import {useEffect, useRef, useState} from 'react';\nimport {getClientRect, ClientRect} from '@dnd-kit/core';\nimport {Transform, useIsomorphicLayoutEffect} from '@dnd-kit/utilities';\n\ninterface Arguments {\n  rect: React.MutableRefObject<ClientRect | null>;\n  disabled: boolean;\n  index: number;\n  node: React.MutableRefObject<HTMLElement | null>;\n}\n\n/*\n * When the index of an item changes while sorting,\n * we need to temporarily disable the transforms\n */\nexport function useDerivedTransform({disabled, index, node, rect}: Arguments) {\n  const [derivedTransform, setDerivedtransform] = useState<Transform | null>(\n    null\n  );\n  const previousIndex = useRef(index);\n\n  useIsomorphicLayoutEffect(() => {\n    if (!disabled && index !== previousIndex.current && node.current) {\n      const initial = rect.current;\n\n      if (initial) {\n        const current = getClientRect(node.current, {\n          ignoreTransform: true,\n        });\n\n        const delta = {\n          x: initial.left - current.left,\n          y: initial.top - current.top,\n          scaleX: initial.width / current.width,\n          scaleY: initial.height / current.height,\n        };\n\n        if (delta.x || delta.y) {\n          setDerivedtransform(delta);\n        }\n      }\n    }\n\n    if (index !== previousIndex.current) {\n      previousIndex.current = index;\n    }\n  }, [disabled, index, node, rect]);\n\n  useEffect(() => {\n    if (derivedTransform) {\n      setDerivedtransform(null);\n    }\n  }, [derivedTransform]);\n\n  return derivedTransform;\n}\n", "import {useContext, useEffect, useMemo, useRef} from 'react';\nimport {\n  useDraggable,\n  useDroppable,\n  UseDraggableArguments,\n  UseDroppableArguments,\n} from '@dnd-kit/core';\nimport type {Data} from '@dnd-kit/core';\nimport {CSS, isKeyboardEvent, useCombinedRefs} from '@dnd-kit/utilities';\n\nimport {Context} from '../components';\nimport type {Disabled, SortableData, SortingStrategy} from '../types';\nimport {isValidIndex} from '../utilities';\nimport {\n  defaultAnimateLayoutChanges,\n  defaultAttributes,\n  defaultNewIndexGetter,\n  defaultTransition,\n  disabledTransition,\n  transitionProperty,\n} from './defaults';\nimport type {\n  AnimateLayoutChanges,\n  NewIndexGetter,\n  SortableTransition,\n} from './types';\nimport {useDerivedTransform} from './utilities';\n\nexport interface Arguments\n  extends Omit<UseDraggableArguments, 'disabled'>,\n    Pick<UseDroppableArguments, 'resizeObserverConfig'> {\n  animateLayoutChanges?: AnimateLayoutChanges;\n  disabled?: boolean | Disabled;\n  getNewIndex?: NewIndexGetter;\n  strategy?: SortingStrategy;\n  transition?: SortableTransition | null;\n}\n\nexport function useSortable({\n  animateLayoutChanges = defaultAnimateLayoutChanges,\n  attributes: userDefinedAttributes,\n  disabled: localDisabled,\n  data: customData,\n  getNewIndex = defaultNewIndexGetter,\n  id,\n  strategy: localStrategy,\n  resizeObserverConfig,\n  transition = defaultTransition,\n}: Arguments) {\n  const {\n    items,\n    containerId,\n    activeIndex,\n    disabled: globalDisabled,\n    disableTransforms,\n    sortedRects,\n    overIndex,\n    useDragOverlay,\n    strategy: globalStrategy,\n  } = useContext(Context);\n  const disabled: Disabled = normalizeLocalDisabled(\n    localDisabled,\n    globalDisabled\n  );\n  const index = items.indexOf(id);\n  const data = useMemo<SortableData & Data>(\n    () => ({sortable: {containerId, index, items}, ...customData}),\n    [containerId, customData, index, items]\n  );\n  const itemsAfterCurrentSortable = useMemo(\n    () => items.slice(items.indexOf(id)),\n    [items, id]\n  );\n  const {\n    rect,\n    node,\n    isOver,\n    setNodeRef: setDroppableNodeRef,\n  } = useDroppable({\n    id,\n    data,\n    disabled: disabled.droppable,\n    resizeObserverConfig: {\n      updateMeasurementsFor: itemsAfterCurrentSortable,\n      ...resizeObserverConfig,\n    },\n  });\n  const {\n    active,\n    activatorEvent,\n    activeNodeRect,\n    attributes,\n    setNodeRef: setDraggableNodeRef,\n    listeners,\n    isDragging,\n    over,\n    setActivatorNodeRef,\n    transform,\n  } = useDraggable({\n    id,\n    data,\n    attributes: {\n      ...defaultAttributes,\n      ...userDefinedAttributes,\n    },\n    disabled: disabled.draggable,\n  });\n  const setNodeRef = useCombinedRefs(setDroppableNodeRef, setDraggableNodeRef);\n  const isSorting = Boolean(active);\n  const displaceItem =\n    isSorting &&\n    !disableTransforms &&\n    isValidIndex(activeIndex) &&\n    isValidIndex(overIndex);\n  const shouldDisplaceDragSource = !useDragOverlay && isDragging;\n  const dragSourceDisplacement =\n    shouldDisplaceDragSource && displaceItem ? transform : null;\n  const strategy = localStrategy ?? globalStrategy;\n  const finalTransform = displaceItem\n    ? dragSourceDisplacement ??\n      strategy({\n        rects: sortedRects,\n        activeNodeRect,\n        activeIndex,\n        overIndex,\n        index,\n      })\n    : null;\n  const newIndex =\n    isValidIndex(activeIndex) && isValidIndex(overIndex)\n      ? getNewIndex({id, items, activeIndex, overIndex})\n      : index;\n  const activeId = active?.id;\n  const previous = useRef({\n    activeId,\n    items,\n    newIndex,\n    containerId,\n  });\n  const itemsHaveChanged = items !== previous.current.items;\n  const shouldAnimateLayoutChanges = animateLayoutChanges({\n    active,\n    containerId,\n    isDragging,\n    isSorting,\n    id,\n    index,\n    items,\n    newIndex: previous.current.newIndex,\n    previousItems: previous.current.items,\n    previousContainerId: previous.current.containerId,\n    transition,\n    wasDragging: previous.current.activeId != null,\n  });\n\n  const derivedTransform = useDerivedTransform({\n    disabled: !shouldAnimateLayoutChanges,\n    index,\n    node,\n    rect,\n  });\n\n  useEffect(() => {\n    if (isSorting && previous.current.newIndex !== newIndex) {\n      previous.current.newIndex = newIndex;\n    }\n\n    if (containerId !== previous.current.containerId) {\n      previous.current.containerId = containerId;\n    }\n\n    if (items !== previous.current.items) {\n      previous.current.items = items;\n    }\n  }, [isSorting, newIndex, containerId, items]);\n\n  useEffect(() => {\n    if (activeId === previous.current.activeId) {\n      return;\n    }\n\n    if (activeId && !previous.current.activeId) {\n      previous.current.activeId = activeId;\n      return;\n    }\n\n    const timeoutId = setTimeout(() => {\n      previous.current.activeId = activeId;\n    }, 50);\n\n    return () => clearTimeout(timeoutId);\n  }, [activeId]);\n\n  return {\n    active,\n    activeIndex,\n    attributes,\n    data,\n    rect,\n    index,\n    newIndex,\n    items,\n    isOver,\n    isSorting,\n    isDragging,\n    listeners,\n    node,\n    overIndex,\n    over,\n    setNodeRef,\n    setActivatorNodeRef,\n    setDroppableNodeRef,\n    setDraggableNodeRef,\n    transform: derivedTransform ?? finalTransform,\n    transition: getTransition(),\n  };\n\n  function getTransition() {\n    if (\n      // Temporarily disable transitions for a single frame to set up derived transforms\n      derivedTransform ||\n      // Or to prevent items jumping to back to their \"new\" position when items change\n      (itemsHaveChanged && previous.current.newIndex === index)\n    ) {\n      return disabledTransition;\n    }\n\n    if (\n      (shouldDisplaceDragSource && !isKeyboardEvent(activatorEvent)) ||\n      !transition\n    ) {\n      return undefined;\n    }\n\n    if (isSorting || shouldAnimateLayoutChanges) {\n      return CSS.Transition.toString({\n        ...transition,\n        property: transitionProperty,\n      });\n    }\n\n    return undefined;\n  }\n}\n\nfunction normalizeLocalDisabled(\n  localDisabled: Arguments['disabled'],\n  globalDisabled: Disabled\n) {\n  if (typeof localDisabled === 'boolean') {\n    return {\n      draggable: localDisabled,\n      // Backwards compatibility\n      droppable: false,\n    };\n  }\n\n  return {\n    draggable: localDisabled?.draggable ?? globalDisabled.draggable,\n    droppable: localDisabled?.droppable ?? globalDisabled.droppable,\n  };\n}\n", "import type {\n  Active,\n  Data,\n  DroppableContainer,\n  DraggableNode,\n  Over,\n} from '@dnd-kit/core';\n\nimport type {SortableData} from './data';\n\nexport function hasSortableData<\n  T extends Active | Over | DraggableNode | DroppableContainer\n>(\n  entry: T | null | undefined\n): entry is T & {data: {current: Data<SortableData>}} {\n  if (!entry) {\n    return false;\n  }\n\n  const data = entry.data.current;\n\n  if (\n    data &&\n    'sortable' in data &&\n    typeof data.sortable === 'object' &&\n    'containerId' in data.sortable &&\n    'items' in data.sortable &&\n    'index' in data.sortable\n  ) {\n    return true;\n  }\n\n  return false;\n}\n", "import {\n  closestCorners,\n  getScrollableAncestors,\n  getFirstCollision,\n  KeyboardCode,\n  DroppableContainer,\n  KeyboardCoordinateGetter,\n} from '@dnd-kit/core';\nimport {subtract} from '@dnd-kit/utilities';\n\nimport {hasSortableData} from '../../types';\n\nconst directions: string[] = [\n  KeyboardCode.Down,\n  KeyboardCode.Right,\n  KeyboardCode.Up,\n  KeyboardCode.Left,\n];\n\nexport const sortableKeyboardCoordinates: KeyboardCoordinateGetter = (\n  event,\n  {\n    context: {\n      active,\n      collisionRect,\n      droppableRects,\n      droppableContainers,\n      over,\n      scrollableAncestors,\n    },\n  }\n) => {\n  if (directions.includes(event.code)) {\n    event.preventDefault();\n\n    if (!active || !collisionRect) {\n      return;\n    }\n\n    const filteredContainers: DroppableContainer[] = [];\n\n    droppableContainers.getEnabled().forEach((entry) => {\n      if (!entry || entry?.disabled) {\n        return;\n      }\n\n      const rect = droppableRects.get(entry.id);\n\n      if (!rect) {\n        return;\n      }\n\n      switch (event.code) {\n        case KeyboardCode.Down:\n          if (collisionRect.top < rect.top) {\n            filteredContainers.push(entry);\n          }\n          break;\n        case KeyboardCode.Up:\n          if (collisionRect.top > rect.top) {\n            filteredContainers.push(entry);\n          }\n          break;\n        case KeyboardCode.Left:\n          if (collisionRect.left > rect.left) {\n            filteredContainers.push(entry);\n          }\n          break;\n        case KeyboardCode.Right:\n          if (collisionRect.left < rect.left) {\n            filteredContainers.push(entry);\n          }\n          break;\n      }\n    });\n\n    const collisions = closestCorners({\n      active,\n      collisionRect: collisionRect,\n      droppableRects,\n      droppableContainers: filteredContainers,\n      pointerCoordinates: null,\n    });\n    let closestId = getFirstCollision(collisions, 'id');\n\n    if (closestId === over?.id && collisions.length > 1) {\n      closestId = collisions[1].id;\n    }\n\n    if (closestId != null) {\n      const activeDroppable = droppableContainers.get(active.id);\n      const newDroppable = droppableContainers.get(closestId);\n      const newRect = newDroppable ? droppableRects.get(newDroppable.id) : null;\n      const newNode = newDroppable?.node.current;\n\n      if (newNode && newRect && activeDroppable && newDroppable) {\n        const newScrollAncestors = getScrollableAncestors(newNode);\n        const hasDifferentScrollAncestors = newScrollAncestors.some(\n          (element, index) => scrollableAncestors[index] !== element\n        );\n        const hasSameContainer = isSameContainer(activeDroppable, newDroppable);\n        const isAfterActive = isAfter(activeDroppable, newDroppable);\n        const offset =\n          hasDifferentScrollAncestors || !hasSameContainer\n            ? {\n                x: 0,\n                y: 0,\n              }\n            : {\n                x: isAfterActive ? collisionRect.width - newRect.width : 0,\n                y: isAfterActive ? collisionRect.height - newRect.height : 0,\n              };\n        const rectCoordinates = {\n          x: newRect.left,\n          y: newRect.top,\n        };\n\n        const newCoordinates =\n          offset.x && offset.y\n            ? rectCoordinates\n            : subtract(rectCoordinates, offset);\n\n        return newCoordinates;\n      }\n    }\n  }\n\n  return undefined;\n};\n\nfunction isSameContainer(a: DroppableContainer, b: DroppableContainer) {\n  if (!hasSortableData(a) || !hasSortableData(b)) {\n    return false;\n  }\n\n  return (\n    a.data.current.sortable.containerId === b.data.current.sortable.containerId\n  );\n}\n\nfunction isAfter(a: DroppableContainer, b: DroppableContainer) {\n  if (!hasSortableData(a) || !hasSortableData(b)) {\n    return false;\n  }\n\n  if (!isSameContainer(a, b)) {\n    return false;\n  }\n\n  return a.data.current.sortable.index < b.data.current.sortable.index;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;SAGgBA,UAAaC,OAAYC,MAAcC,IAAAA;AACrD,QAAMC,WAAWH,MAAMI,MAAN;AACjBD,WAASE,OACPH,KAAK,IAAIC,SAASG,SAASJ,KAAKA,IAChC,GACAC,SAASE,OAAOJ,MAAM,CAAtB,EAAyB,CAAzB,CAHF;AAMA,SAAOE;AACR;ACTD,SAAgBI,UAAaP,OAAYC,MAAcC,IAAAA;AACrD,QAAMC,WAAWH,MAAMI,MAAN;AAEjBD,WAASF,IAAD,IAASD,MAAME,EAAD;AACtBC,WAASD,EAAD,IAAOF,MAAMC,IAAD;AAEpB,SAAOE;AACR;SCJeK,eACdC,OACAC,OAAAA;AAEA,SAAOD,MAAME,OAAqB,CAACC,aAAaC,IAAIC,UAAlB;AAChC,UAAMC,OAAOL,MAAMM,IAAIH,EAAV;AAEb,QAAIE,MAAM;AACRH,kBAAYE,KAAD,IAAUC;;AAGvB,WAAOH;KACNK,MAAMR,MAAMH,MAAP,CARD;AASR;SCnBeY,aAAaJ,OAAAA;AAC3B,SAAOA,UAAU,QAAQA,SAAS;AACnC;SCAeK,WAAWC,GAAuBC,GAAAA;AAChD,MAAID,MAAMC,GAAG;AACX,WAAO;;AAGT,MAAID,EAAEd,WAAWe,EAAEf,QAAQ;AACzB,WAAO;;AAGT,WAASgB,IAAI,GAAGA,IAAIF,EAAEd,QAAQgB,KAAK;AACjC,QAAIF,EAAEE,CAAD,MAAQD,EAAEC,CAAD,GAAK;AACjB,aAAO;;;AAIX,SAAO;AACR;SChBeC,kBAAkBC,UAAAA;AAChC,MAAI,OAAOA,aAAa,WAAW;AACjC,WAAO;MACLC,WAAWD;MACXE,WAAWF;;;AAIf,SAAOA;AACR;ACPD,IAAMG,eAAe;EACnBC,QAAQ;EACRC,QAAQ;AAFW;AAKrB,IAAaC,gCAAiD,UAAA;;MAAC;IAC7DpB;IACAqB,gBAAgBC;IAChBC;IACAC;IACApB;;AAEA,QAAMiB,kBAAc,qBAAGrB,MAAMuB,WAAD,MAAR,OAAA,qBAAyBD;AAE7C,MAAI,CAACD,gBAAgB;AACnB,WAAO;;AAGT,QAAMI,UAAUC,WAAW1B,OAAOI,OAAOmB,WAAf;AAE1B,MAAInB,UAAUmB,aAAa;AACzB,UAAMI,eAAe3B,MAAMwB,SAAD;AAE1B,QAAI,CAACG,cAAc;AACjB,aAAO;;AAGT,WAAO;MACLC,GACEL,cAAcC,YACVG,aAAaE,OACbF,aAAaG,SACZT,eAAeQ,OAAOR,eAAeS,SACtCH,aAAaE,OAAOR,eAAeQ;MACzCE,GAAG;MACH,GAAGd;;;AAIP,MAAIb,QAAQmB,eAAenB,SAASoB,WAAW;AAC7C,WAAO;MACLI,GAAG,CAACP,eAAeS,QAAQL;MAC3BM,GAAG;MACH,GAAGd;;;AAIP,MAAIb,QAAQmB,eAAenB,SAASoB,WAAW;AAC7C,WAAO;MACLI,GAAGP,eAAeS,QAAQL;MAC1BM,GAAG;MACH,GAAGd;;;AAIP,SAAO;IACLW,GAAG;IACHG,GAAG;IACH,GAAGd;;AAEN;AAED,SAASS,WAAW1B,OAAqBI,OAAemB,aAAxD;AACE,QAAMS,cAAsChC,MAAMI,KAAD;AACjD,QAAM6B,eAAuCjC,MAAMI,QAAQ,CAAT;AAClD,QAAM8B,WAAmClC,MAAMI,QAAQ,CAAT;AAE9C,MAAI,CAAC4B,eAAgB,CAACC,gBAAgB,CAACC,UAAW;AAChD,WAAO;;AAGT,MAAIX,cAAcnB,OAAO;AACvB,WAAO6B,eACHD,YAAYH,QAAQI,aAAaJ,OAAOI,aAAaH,SACrDI,SAASL,QAAQG,YAAYH,OAAOG,YAAYF;;AAGtD,SAAOI,WACHA,SAASL,QAAQG,YAAYH,OAAOG,YAAYF,SAChDE,YAAYH,QAAQI,aAAaJ,OAAOI,aAAaH;AAC1D;ICjFYK,sBAAuC,UAAA;MAAC;IACnDnC;IACAuB;IACAC;IACApB;;AAEA,QAAMgC,WAAW/C,UAAUW,OAAOwB,WAAWD,WAAnB;AAE1B,QAAMc,UAAUrC,MAAMI,KAAD;AACrB,QAAMkC,UAAUF,SAAShC,KAAD;AAExB,MAAI,CAACkC,WAAW,CAACD,SAAS;AACxB,WAAO;;AAGT,SAAO;IACLT,GAAGU,QAAQT,OAAOQ,QAAQR;IAC1BE,GAAGO,QAAQC,MAAMF,QAAQE;IACzBrB,QAAQoB,QAAQR,QAAQO,QAAQP;IAChCX,QAAQmB,QAAQE,SAASH,QAAQG;;AAEpC;ICtBYC,uBAAwC,UAAA;MAAC;IACpDlB;IACAnB;IACAJ;IACAwB;;AAEA,MAAIa;AACJ,MAAIC;AAEJ,MAAIlC,UAAUmB,aAAa;AACzBc,cAAUrC,MAAMI,KAAD;AACfkC,cAAUtC,MAAMwB,SAAD;;AAGjB,MAAIpB,UAAUoB,WAAW;AACvBa,cAAUrC,MAAMI,KAAD;AACfkC,cAAUtC,MAAMuB,WAAD;;AAGjB,MAAI,CAACe,WAAW,CAACD,SAAS;AACxB,WAAO;;AAGT,SAAO;IACLT,GAAGU,QAAQT,OAAOQ,QAAQR;IAC1BE,GAAGO,QAAQC,MAAMF,QAAQE;IACzBrB,QAAQoB,QAAQR,QAAQO,QAAQP;IAChCX,QAAQmB,QAAQE,SAASH,QAAQG;;AAEpC;AC3BD,IAAMvB,iBAAe;EACnBC,QAAQ;EACRC,QAAQ;AAFW;AAKrB,IAAauB,8BAA+C,UAAA;;MAAC;IAC3DnB;IACAF,gBAAgBC;IAChBlB;IACAJ;IACAwB;;AAEA,QAAMH,kBAAc,qBAAGrB,MAAMuB,WAAD,MAAR,OAAA,qBAAyBD;AAE7C,MAAI,CAACD,gBAAgB;AACnB,WAAO;;AAGT,MAAIjB,UAAUmB,aAAa;AACzB,UAAMoB,gBAAgB3C,MAAMwB,SAAD;AAE3B,QAAI,CAACmB,eAAe;AAClB,aAAO;;AAGT,WAAO;MACLf,GAAG;MACHG,GACER,cAAcC,YACVmB,cAAcJ,MACdI,cAAcH,UACbnB,eAAekB,MAAMlB,eAAemB,UACrCG,cAAcJ,MAAMlB,eAAekB;MACzC,GAAGtB;;;AAIP,QAAMQ,UAAUC,aAAW1B,OAAOI,OAAOmB,WAAf;AAE1B,MAAInB,QAAQmB,eAAenB,SAASoB,WAAW;AAC7C,WAAO;MACLI,GAAG;MACHG,GAAG,CAACV,eAAemB,SAASf;MAC5B,GAAGR;;;AAIP,MAAIb,QAAQmB,eAAenB,SAASoB,WAAW;AAC7C,WAAO;MACLI,GAAG;MACHG,GAAGV,eAAemB,SAASf;MAC3B,GAAGR;;;AAIP,SAAO;IACLW,GAAG;IACHG,GAAG;IACH,GAAGd;;AAEN;AAED,SAASS,aACPkB,aACAxC,OACAmB,aAHF;AAKE,QAAMS,cAAsCY,YAAYxC,KAAD;AACvD,QAAM6B,eAAuCW,YAAYxC,QAAQ,CAAT;AACxD,QAAM8B,WAAmCU,YAAYxC,QAAQ,CAAT;AAEpD,MAAI,CAAC4B,aAAa;AAChB,WAAO;;AAGT,MAAIT,cAAcnB,OAAO;AACvB,WAAO6B,eACHD,YAAYO,OAAON,aAAaM,MAAMN,aAAaO,UACnDN,WACAA,SAASK,OAAOP,YAAYO,MAAMP,YAAYQ,UAC9C;;AAGN,SAAON,WACHA,SAASK,OAAOP,YAAYO,MAAMP,YAAYQ,UAC9CP,eACAD,YAAYO,OAAON,aAAaM,MAAMN,aAAaO,UACnD;AACL;AC5ED,IAAMK,YAAY;AAcX,IAAMC,UAAUC,aAAAA,QAAMC,cAAiC;EAC5DzB,aAAa;EACb0B,aAAaJ;EACbK,mBAAmB;EACnBnD,OAAO,CAAA;EACPyB,WAAW;EACX2B,gBAAgB;EAChBC,aAAa,CAAA;EACbC,UAAUlB;EACVrB,UAAU;IACRC,WAAW;IACXC,WAAW;;AAX+C,CAAvC;AAevB,SAAgBsC,gBAAAA,MAAAA;MAAgB;IAC9BC;IACApD;IACAJ,OAAOyD;IACPH,WAAWlB;IACXrB,UAAU2C,eAAe;;AAEzB,QAAM;IACJC;IACAC;IACAC;IACAC;IACAC;MACEC,cAAa;AACjB,QAAMd,cAAce,YAAYnB,WAAW1C,EAAZ;AAC/B,QAAMgD,iBAAiBc,QAAQN,YAAYtD,SAAS,IAAtB;AAC9B,QAAMN,YAAQmE,sBACZ,MACEV,iBAAiBW,IAAKC,UACpB,OAAOA,SAAS,YAAY,QAAQA,OAAOA,KAAKjE,KAAKiE,IADvD,GAGF,CAACZ,gBAAD,CALmB;AAOrB,QAAMa,aAAaX,UAAU;AAC7B,QAAMnC,cAAcmC,SAAS3D,MAAMuE,QAAQZ,OAAOvD,EAArB,IAA2B;AACxD,QAAMqB,YAAYqC,OAAO9D,MAAMuE,QAAQT,KAAK1D,EAAnB,IAAyB;AAClD,QAAMoE,uBAAmBC,qBAAOzE,KAAD;AAC/B,QAAM0E,mBAAmB,CAAChE,WAAWV,OAAOwE,iBAAiBG,OAAzB;AACpC,QAAMxB,oBACH1B,cAAc,MAAMD,gBAAgB,MAAOkD;AAC9C,QAAM3D,WAAWD,kBAAkB4C,YAAD;AAElCkB,4BAA0B,MAAA;AACxB,QAAIF,oBAAoBJ,YAAY;AAClCP,iCAA2B/D,KAAD;;KAE3B,CAAC0E,kBAAkB1E,OAAOsE,YAAYP,0BAAtC,CAJsB;AAMzBc,8BAAU,MAAA;AACRL,qBAAiBG,UAAU3E;KAC1B,CAACA,KAAD,CAFM;AAIT,QAAM8E,mBAAeX;IACnB,OAA0B;MACxB3C;MACA0B;MACAnC;MACAoC;MACAnD;MACAyB;MACA2B;MACAC,aAAatD,eAAeC,OAAO6D,cAAR;MAC3BP;;;IAGF,CACE9B,aACA0B,aACAnC,SAASC,WACTD,SAASE,WACTkC,mBACAnD,OACAyB,WACAoC,gBACAT,gBACAE,QAVF;EAb0B;AA2B5B,SAAON,aAAAA,QAAAA,cAACD,QAAQgC,UAAT;IAAkBC,OAAOF;KAAetB,QAAxC;AACR;ICzGYyB,wBAAwC,UAAA;AAAA,MAAC;IACpD7E;IACAJ;IACAwB;IACAC;MAJmD;AAAA,SAK/CnC,UAAUU,OAAOwB,aAAaC,SAArB,EAAgC8C,QAAQnE,EAAjD;AAL+C;AAOrD,IAAa8E,8BAAoD,WAAA;MAAC;IAChEhC;IACAiC;IACAC;IACA/E;IACAL;IACAqF;IACAC;IACAC;IACAC;;AAEA,MAAI,CAACA,cAAc,CAACJ,aAAa;AAC/B,WAAO;;AAGT,MAAIE,kBAAkBtF,SAASK,UAAUgF,UAAU;AACjD,WAAO;;AAGT,MAAIF,WAAW;AACb,WAAO;;AAGT,SAAOE,aAAahF,SAAS6C,gBAAgBqC;AAC9C;AAEM,IAAME,oBAAwC;EACnDC,UAAU;EACVC,QAAQ;AAF2C;AAK9C,IAAMC,qBAAqB;AAE3B,IAAMC,qBAAqBC,IAAIC,WAAWC,SAAS;EACxDC,UAAUL;EACVF,UAAU;EACVC,QAAQ;AAHgD,CAAxB;AAM3B,IAAMO,oBAAoB;EAC/BC,iBAAiB;AADc;ACzCjC,SAAgBC,oBAAAA,MAAAA;MAAoB;IAACrF;IAAUV;IAAOgG;IAAM/F;;AAC1D,QAAM,CAACgG,kBAAkBC,mBAAnB,QAA0CC,uBAC9C,IADsD;AAGxD,QAAMC,oBAAgBhC,qBAAOpE,KAAD;AAE5BuE,4BAA0B,MAAA;AACxB,QAAI,CAAC7D,YAAYV,UAAUoG,cAAc9B,WAAW0B,KAAK1B,SAAS;AAChE,YAAM+B,UAAUpG,KAAKqE;AAErB,UAAI+B,SAAS;AACX,cAAM/B,UAAUgC,cAAcN,KAAK1B,SAAS;UAC1CiC,iBAAiB;SADU;AAI7B,cAAMC,QAAQ;UACZhF,GAAG6E,QAAQ5E,OAAO6C,QAAQ7C;UAC1BE,GAAG0E,QAAQlE,MAAMmC,QAAQnC;UACzBrB,QAAQuF,QAAQ3E,QAAQ4C,QAAQ5C;UAChCX,QAAQsF,QAAQjE,SAASkC,QAAQlC;;AAGnC,YAAIoE,MAAMhF,KAAKgF,MAAM7E,GAAG;AACtBuE,8BAAoBM,KAAD;;;;AAKzB,QAAIxG,UAAUoG,cAAc9B,SAAS;AACnC8B,oBAAc9B,UAAUtE;;KAEzB,CAACU,UAAUV,OAAOgG,MAAM/F,IAAxB,CAzBsB;AA2BzBuE,8BAAU,MAAA;AACR,QAAIyB,kBAAkB;AACpBC,0BAAoB,IAAD;;KAEpB,CAACD,gBAAD,CAJM;AAMT,SAAOA;AACR;SCjBeQ,YAAAA,MAAAA;MAAY;IAC1BC,uBAAuB7B;IACvB8B,YAAYC;IACZlG,UAAUmG;IACVC,MAAMC;IACNC,cAAcpC;IACd7E;IACAkD,UAAUgE;IACVC;IACA/B,aAAaC;;AAEb,QAAM;IACJzF;IACAkD;IACA1B;IACAT,UAAUyG;IACVrE;IACAE;IACA5B;IACA2B;IACAE,UAAUmE;UACRC,yBAAW3E,OAAD;AACd,QAAMhC,WAAqB4G,uBACzBT,eACAM,cAF+C;AAIjD,QAAMnH,QAAQL,MAAMuE,QAAQnE,EAAd;AACd,QAAM+G,WAAOhD,sBACX,OAAO;IAACyD,UAAU;MAAC1E;MAAa7C;MAAOL;;IAAQ,GAAGoH;MAClD,CAAClE,aAAakE,YAAY/G,OAAOL,KAAjC,CAFkB;AAIpB,QAAM6H,gCAA4B1D,sBAChC,MAAMnE,MAAML,MAAMK,MAAMuE,QAAQnE,EAAd,CAAZ,GACN,CAACJ,OAAOI,EAAR,CAFuC;AAIzC,QAAM;IACJE;IACA+F;IACAyB;IACAC,YAAYC;MACVC,aAAa;IACf7H;IACA+G;IACApG,UAAUA,SAASE;IACnBsG,sBAAsB;MACpBW,uBAAuBL;MACvB,GAAGN;;GANS;AAShB,QAAM;IACJ5D;IACAwE;IACA7G;IACA0F;IACAe,YAAYK;IACZC;IACA/D;IACAR;IACAwE;IACAC;MACEC,aAAa;IACfpI;IACA+G;IACAH,YAAY;MACV,GAAGd;MACH,GAAGe;;IAELlG,UAAUA,SAASC;GAPL;AAShB,QAAM+G,aAAaU,gBAAgBT,qBAAqBI,mBAAtB;AAClC,QAAMjD,YAAYjB,QAAQP,MAAD;AACzB,QAAM+E,eACJvD,aACA,CAAChC,qBACD1C,aAAae,WAAD,KACZf,aAAagB,SAAD;AACd,QAAMkH,2BAA2B,CAACvF,kBAAkBkB;AACpD,QAAMsE,yBACJD,4BAA4BD,eAAeH,YAAY;AACzD,QAAMjF,WAAWgE,iBAAH,OAAGA,gBAAiBG;AAClC,QAAMoB,iBAAiBH,eACnBE,0BAD+B,OAC/BA,yBACAtF,SAAS;IACPrD,OAAOoD;IACP/B;IACAE;IACAC;IACApB;GALM,IAOR;AACJ,QAAMgF,WACJ5E,aAAae,WAAD,KAAiBf,aAAagB,SAAD,IACrC4F,YAAY;IAACjH;IAAIJ;IAAOwB;IAAaC;GAA1B,IACXpB;AACN,QAAMyI,WAAWnF,UAAH,OAAA,SAAGA,OAAQvD;AACzB,QAAM2I,eAAWtE,qBAAO;IACtBqE;IACA9I;IACAqF;IACAnC;GAJqB;AAMvB,QAAMwB,mBAAmB1E,UAAU+I,SAASpE,QAAQ3E;AACpD,QAAMgJ,6BAA6BjC,qBAAqB;IACtDpD;IACAT;IACAoB;IACAa;IACA/E;IACAC;IACAL;IACAqF,UAAU0D,SAASpE,QAAQU;IAC3BC,eAAeyD,SAASpE,QAAQ3E;IAChCuF,qBAAqBwD,SAASpE,QAAQzB;IACtCsC;IACAJ,aAAa2D,SAASpE,QAAQmE,YAAY;GAZW;AAevD,QAAMxC,mBAAmBF,oBAAoB;IAC3CrF,UAAU,CAACiI;IACX3I;IACAgG;IACA/F;GAJ0C;AAO5CuE,8BAAU,MAAA;AACR,QAAIM,aAAa4D,SAASpE,QAAQU,aAAaA,UAAU;AACvD0D,eAASpE,QAAQU,WAAWA;;AAG9B,QAAInC,gBAAgB6F,SAASpE,QAAQzB,aAAa;AAChD6F,eAASpE,QAAQzB,cAAcA;;AAGjC,QAAIlD,UAAU+I,SAASpE,QAAQ3E,OAAO;AACpC+I,eAASpE,QAAQ3E,QAAQA;;KAE1B,CAACmF,WAAWE,UAAUnC,aAAalD,KAAnC,CAZM;AAcT6E,8BAAU,MAAA;AACR,QAAIiE,aAAaC,SAASpE,QAAQmE,UAAU;AAC1C;;AAGF,QAAIA,YAAY,CAACC,SAASpE,QAAQmE,UAAU;AAC1CC,eAASpE,QAAQmE,WAAWA;AAC5B;;AAGF,UAAMG,YAAYC,WAAW,MAAA;AAC3BH,eAASpE,QAAQmE,WAAWA;OAC3B,EAFyB;AAI5B,WAAO,MAAMK,aAAaF,SAAD;KACxB,CAACH,QAAD,CAfM;AAiBT,SAAO;IACLnF;IACAnC;IACAwF;IACAG;IACA7G;IACAD;IACAgF;IACArF;IACA8H;IACA3C;IACAb;IACA+D;IACAhC;IACA5E;IACAqC;IACAiE;IACAO;IACAN;IACAI;IACAG,WAAWjC,oBAAF,OAAEA,mBAAoBuC;IAC/BrD,YAAY4D,cAAa;;AAG3B,WAASA,gBAAT;AACE;;MAEE9C;MAEC5B,oBAAoBqE,SAASpE,QAAQU,aAAahF;MACnD;AACA,aAAOwF;;AAGT,QACG8C,4BAA4B,CAACU,gBAAgBlB,cAAD,KAC7C,CAAC3C,YACD;AACA,aAAO8D;;AAGT,QAAInE,aAAa6D,4BAA4B;AAC3C,aAAOlD,IAAIC,WAAWC,SAAS;QAC7B,GAAGR;QACHS,UAAUL;OAFL;;AAMT,WAAO0D;;AAEV;AAED,SAAS3B,uBACPT,eACAM,gBAFF;;AAIE,MAAI,OAAON,kBAAkB,WAAW;AACtC,WAAO;MACLlG,WAAWkG;;MAEXjG,WAAW;;;AAIf,SAAO;IACLD,YAAS,wBAAEkG,iBAAF,OAAA,SAAEA,cAAelG,cAAjB,OAAA,wBAA8BwG,eAAexG;IACtDC,YAAS,wBAAEiG,iBAAF,OAAA,SAAEA,cAAejG,cAAjB,OAAA,wBAA8BuG,eAAevG;;AAEzD;SC3PesI,gBAGdC,OAAAA;AAEA,MAAI,CAACA,OAAO;AACV,WAAO;;AAGT,QAAMrC,OAAOqC,MAAMrC,KAAKxC;AAExB,MACEwC,QACA,cAAcA,QACd,OAAOA,KAAKS,aAAa,YACzB,iBAAiBT,KAAKS,YACtB,WAAWT,KAAKS,YAChB,WAAWT,KAAKS,UAChB;AACA,WAAO;;AAGT,SAAO;AACR;ACrBD,IAAM6B,aAAuB,CAC3BC,aAAaC,MACbD,aAAaE,OACbF,aAAaG,IACbH,aAAaI,IAJc;AAO7B,IAAaC,8BAAwD,CACnEC,OADmE,SAAA;MAEnE;IACEC,SAAS;MACPtG;MACAuG;MACArG;MACAsG;MACArG;MACAsG;;;AAIJ,MAAIX,WAAWY,SAASL,MAAMM,IAA1B,GAAiC;AACnCN,UAAMO,eAAN;AAEA,QAAI,CAAC5G,UAAU,CAACuG,eAAe;AAC7B;;AAGF,UAAMM,qBAA2C,CAAA;AAEjDL,wBAAoBM,WAApB,EAAiCC,QAASlB,WAAD;AACvC,UAAI,CAACA,SAASA,SAAV,QAAUA,MAAOzI,UAAU;AAC7B;;AAGF,YAAMT,OAAOuD,eAAetD,IAAIiJ,MAAMpJ,EAAzB;AAEb,UAAI,CAACE,MAAM;AACT;;AAGF,cAAQ0J,MAAMM,MAAd;QACE,KAAKZ,aAAaC;AAChB,cAAIO,cAAc1H,MAAMlC,KAAKkC,KAAK;AAChCgI,+BAAmBG,KAAKnB,KAAxB;;AAEF;QACF,KAAKE,aAAaG;AAChB,cAAIK,cAAc1H,MAAMlC,KAAKkC,KAAK;AAChCgI,+BAAmBG,KAAKnB,KAAxB;;AAEF;QACF,KAAKE,aAAaI;AAChB,cAAII,cAAcpI,OAAOxB,KAAKwB,MAAM;AAClC0I,+BAAmBG,KAAKnB,KAAxB;;AAEF;QACF,KAAKE,aAAaE;AAChB,cAAIM,cAAcpI,OAAOxB,KAAKwB,MAAM;AAClC0I,+BAAmBG,KAAKnB,KAAxB;;AAEF;;KA/BN;AAmCA,UAAMoB,aAAaC,eAAe;MAChClH;MACAuG;MACArG;MACAsG,qBAAqBK;MACrBM,oBAAoB;KALW;AAOjC,QAAIC,YAAYC,kBAAkBJ,YAAY,IAAb;AAEjC,QAAIG,eAAcjH,QAAL,OAAA,SAAKA,KAAM1D,OAAMwK,WAAW/K,SAAS,GAAG;AACnDkL,kBAAYH,WAAW,CAAD,EAAIxK;;AAG5B,QAAI2K,aAAa,MAAM;AACrB,YAAME,kBAAkBd,oBAAoB5J,IAAIoD,OAAOvD,EAA/B;AACxB,YAAM8K,eAAef,oBAAoB5J,IAAIwK,SAAxB;AACrB,YAAMxI,UAAU2I,eAAerH,eAAetD,IAAI2K,aAAa9K,EAAhC,IAAsC;AACrE,YAAM+K,UAAUD,gBAAH,OAAA,SAAGA,aAAc7E,KAAK1B;AAEnC,UAAIwG,WAAW5I,WAAW0I,mBAAmBC,cAAc;AACzD,cAAME,qBAAqBC,uBAAuBF,OAAD;AACjD,cAAMG,8BAA8BF,mBAAmBG,KACrD,CAACC,SAASnL,UAAU+J,oBAAoB/J,KAAD,MAAYmL,OADjB;AAGpC,cAAMC,mBAAmBC,gBAAgBT,iBAAiBC,YAAlB;AACxC,cAAMS,gBAAgBC,QAAQX,iBAAiBC,YAAlB;AAC7B,cAAMW,SACJP,+BAA+B,CAACG,mBAC5B;UACE5J,GAAG;UACHG,GAAG;YAEL;UACEH,GAAG8J,gBAAgBzB,cAAcnI,QAAQQ,QAAQR,QAAQ;UACzDC,GAAG2J,gBAAgBzB,cAAczH,SAASF,QAAQE,SAAS;;AAEnE,cAAMqJ,kBAAkB;UACtBjK,GAAGU,QAAQT;UACXE,GAAGO,QAAQC;;AAGb,cAAMuJ,iBACJF,OAAOhK,KAAKgK,OAAO7J,IACf8J,kBACAE,SAASF,iBAAiBD,MAAlB;AAEd,eAAOE;;;;AAKb,SAAOzC;AACR;AAED,SAASoC,gBAAgB/K,GAAuBC,GAAhD;AACE,MAAI,CAAC2I,gBAAgB5I,CAAD,KAAO,CAAC4I,gBAAgB3I,CAAD,GAAK;AAC9C,WAAO;;AAGT,SACED,EAAEwG,KAAKxC,QAAQiD,SAAS1E,gBAAgBtC,EAAEuG,KAAKxC,QAAQiD,SAAS1E;AAEnE;AAED,SAAS0I,QAAQjL,GAAuBC,GAAxC;AACE,MAAI,CAAC2I,gBAAgB5I,CAAD,KAAO,CAAC4I,gBAAgB3I,CAAD,GAAK;AAC9C,WAAO;;AAGT,MAAI,CAAC8K,gBAAgB/K,GAAGC,CAAJ,GAAQ;AAC1B,WAAO;;AAGT,SAAOD,EAAEwG,KAAKxC,QAAQiD,SAASvH,QAAQO,EAAEuG,KAAKxC,QAAQiD,SAASvH;AAChE;", "names": ["arrayMove", "array", "from", "to", "newArray", "slice", "splice", "length", "arraySwap", "getSortedRects", "items", "rects", "reduce", "accumulator", "id", "index", "rect", "get", "Array", "isValidIndex", "itemsEqual", "a", "b", "i", "normalizeDisabled", "disabled", "draggable", "droppable", "defaultScale", "scaleX", "scaleY", "horizontalListSortingStrategy", "activeNodeRect", "fallbackActiveRect", "activeIndex", "overIndex", "itemGap", "getItemGap", "newIndexRect", "x", "left", "width", "y", "currentRect", "previousRect", "nextRect", "rectSortingStrategy", "newRects", "oldRect", "newRect", "top", "height", "rectSwappingStrategy", "verticalListSortingStrategy", "overIndexRect", "clientRects", "ID_PREFIX", "Context", "React", "createContext", "containerId", "disableTransforms", "useDragOverlay", "sortedRects", "strategy", "SortableContext", "children", "userDefinedItems", "disabledProp", "active", "dragOverlay", "droppableRects", "over", "measureDroppableContainers", "useDndContext", "useUniqueId", "Boolean", "useMemo", "map", "item", "isDragging", "indexOf", "previousItemsRef", "useRef", "itemsHaveChanged", "current", "useIsomorphicLayoutEffect", "useEffect", "contextValue", "Provider", "value", "defaultNewIndexGetter", "defaultAnimateLayoutChanges", "isSorting", "wasDragging", "newIndex", "previousItems", "previousContainerId", "transition", "defaultTransition", "duration", "easing", "transitionProperty", "disabledTransition", "CSS", "Transition", "toString", "property", "defaultAttributes", "roleDescription", "useDerivedTransform", "node", "derivedTransform", "setDerivedtransform", "useState", "previousIndex", "initial", "getClientRect", "ignoreTransform", "delta", "useSortable", "animateLayoutChanges", "attributes", "userDefinedAttributes", "localDisabled", "data", "customData", "getNewIndex", "localStrategy", "resizeObserverConfig", "globalDisabled", "globalStrategy", "useContext", "normalizeLocalDisabled", "sortable", "itemsAfterCurrentSortable", "isOver", "setNodeRef", "setDroppableNodeRef", "useDroppable", "updateMeasurementsFor", "activatorEvent", "setDraggableNodeRef", "listeners", "setActivatorNodeRef", "transform", "useDraggable", "useCombinedRefs", "displaceItem", "shouldDisplaceDragSource", "dragSourceDisplacement", "finalTransform", "activeId", "previous", "shouldAnimateLayoutChanges", "timeoutId", "setTimeout", "clearTimeout", "getTransition", "isKeyboardEvent", "undefined", "hasSortableData", "entry", "directions", "KeyboardCode", "Down", "Right", "Up", "Left", "sortableKeyboardCoordinates", "event", "context", "collisionRect", "droppableContainers", "scrollableAncestors", "includes", "code", "preventDefault", "filteredContainers", "getEnabled", "for<PERSON>ach", "push", "collisions", "closestCorners", "pointerCoordinates", "closestId", "getFirstCollision", "activeDroppable", "newDroppable", "newNode", "newScrollAncestors", "getScrollableAncestors", "hasDifferentScrollAncestors", "some", "element", "hasSameContainer", "isSameContainer", "isAfterActive", "isAfter", "offset", "rectCoordinates", "newCoordinates", "subtract"]}