{"version": 3, "sources": ["../../@dnd-kit/accessibility/src/components/HiddenText/HiddenText.tsx", "../../@dnd-kit/accessibility/src/components/LiveRegion/LiveRegion.tsx", "../../@dnd-kit/accessibility/src/hooks/useAnnouncement.ts", "../../@dnd-kit/core/src/components/DndMonitor/context.ts", "../../@dnd-kit/core/src/components/DndMonitor/useDndMonitor.ts", "../../@dnd-kit/core/src/components/DndMonitor/useDndMonitorProvider.tsx", "../../@dnd-kit/core/src/components/Accessibility/defaults.ts", "../../@dnd-kit/core/src/components/Accessibility/Accessibility.tsx", "../../@dnd-kit/core/src/store/actions.ts", "../../@dnd-kit/core/src/utilities/other/noop.ts", "../../@dnd-kit/core/src/sensors/useSensor.ts", "../../@dnd-kit/core/src/sensors/useSensors.ts", "../../@dnd-kit/core/src/utilities/coordinates/constants.ts", "../../@dnd-kit/core/src/utilities/coordinates/distanceBetweenPoints.ts", "../../@dnd-kit/core/src/utilities/coordinates/getRelativeTransformOrigin.ts", "../../@dnd-kit/core/src/utilities/algorithms/helpers.ts", "../../@dnd-kit/core/src/utilities/algorithms/closestCenter.ts", "../../@dnd-kit/core/src/utilities/algorithms/closestCorners.ts", "../../@dnd-kit/core/src/utilities/algorithms/rectIntersection.ts", "../../@dnd-kit/core/src/utilities/algorithms/pointerWithin.ts", "../../@dnd-kit/core/src/utilities/rect/adjustScale.ts", "../../@dnd-kit/core/src/utilities/rect/getRectDelta.ts", "../../@dnd-kit/core/src/utilities/rect/rectAdjustment.ts", "../../@dnd-kit/core/src/utilities/transform/parseTransform.ts", "../../@dnd-kit/core/src/utilities/transform/inverseTransform.ts", "../../@dnd-kit/core/src/utilities/rect/getRect.ts", "../../@dnd-kit/core/src/utilities/rect/getWindowClientRect.ts", "../../@dnd-kit/core/src/utilities/scroll/isFixed.ts", "../../@dnd-kit/core/src/utilities/scroll/isScrollable.ts", "../../@dnd-kit/core/src/utilities/scroll/getScrollableAncestors.ts", "../../@dnd-kit/core/src/utilities/scroll/getScrollableElement.ts", "../../@dnd-kit/core/src/utilities/scroll/getScrollCoordinates.ts", "../../@dnd-kit/core/src/types/direction.ts", "../../@dnd-kit/core/src/utilities/scroll/documentScrollingElement.ts", "../../@dnd-kit/core/src/utilities/scroll/getScrollPosition.ts", "../../@dnd-kit/core/src/utilities/scroll/getScrollDirectionAndSpeed.ts", "../../@dnd-kit/core/src/utilities/scroll/getScrollElementRect.ts", "../../@dnd-kit/core/src/utilities/scroll/getScrollOffsets.ts", "../../@dnd-kit/core/src/utilities/scroll/scrollIntoViewIfNeeded.ts", "../../@dnd-kit/core/src/utilities/rect/Rect.ts", "../../@dnd-kit/core/src/sensors/utilities/Listeners.ts", "../../@dnd-kit/core/src/sensors/utilities/getEventListenerTarget.ts", "../../@dnd-kit/core/src/sensors/utilities/hasExceededDistance.ts", "../../@dnd-kit/core/src/sensors/events.ts", "../../@dnd-kit/core/src/sensors/keyboard/types.ts", "../../@dnd-kit/core/src/sensors/keyboard/defaults.ts", "../../@dnd-kit/core/src/sensors/keyboard/KeyboardSensor.ts", "../../@dnd-kit/core/src/sensors/pointer/AbstractPointerSensor.ts", "../../@dnd-kit/core/src/sensors/pointer/PointerSensor.ts", "../../@dnd-kit/core/src/sensors/mouse/MouseSensor.ts", "../../@dnd-kit/core/src/sensors/touch/TouchSensor.ts", "../../@dnd-kit/core/src/hooks/utilities/useAutoScroller.ts", "../../@dnd-kit/core/src/hooks/utilities/useCachedNode.ts", "../../@dnd-kit/core/src/hooks/utilities/useCombineActivators.ts", "../../@dnd-kit/core/src/hooks/utilities/useDroppableMeasuring.ts", "../../@dnd-kit/core/src/hooks/utilities/useInitialValue.ts", "../../@dnd-kit/core/src/hooks/utilities/useInitialRect.ts", "../../@dnd-kit/core/src/hooks/utilities/useMutationObserver.ts", "../../@dnd-kit/core/src/hooks/utilities/useResizeObserver.ts", "../../@dnd-kit/core/src/hooks/utilities/useRect.ts", "../../@dnd-kit/core/src/hooks/utilities/useRectDelta.ts", "../../@dnd-kit/core/src/hooks/utilities/useScrollableAncestors.ts", "../../@dnd-kit/core/src/hooks/utilities/useScrollOffsets.ts", "../../@dnd-kit/core/src/hooks/utilities/useScrollOffsetsDelta.ts", "../../@dnd-kit/core/src/hooks/utilities/useSensorSetup.ts", "../../@dnd-kit/core/src/hooks/utilities/useSyntheticListeners.ts", "../../@dnd-kit/core/src/hooks/utilities/useWindowRect.ts", "../../@dnd-kit/core/src/hooks/utilities/useRects.ts", "../../@dnd-kit/core/src/utilities/nodes/getMeasurableNode.ts", "../../@dnd-kit/core/src/hooks/utilities/useDragOverlayMeasuring.ts", "../../@dnd-kit/core/src/components/DndContext/defaults.ts", "../../@dnd-kit/core/src/store/constructors.ts", "../../@dnd-kit/core/src/store/context.ts", "../../@dnd-kit/core/src/store/reducer.ts", "../../@dnd-kit/core/src/components/Accessibility/components/RestoreFocus.tsx", "../../@dnd-kit/core/src/modifiers/applyModifiers.ts", "../../@dnd-kit/core/src/components/DndContext/hooks/useMeasuringConfiguration.ts", "../../@dnd-kit/core/src/components/DndContext/hooks/useLayoutShiftScrollCompensation.ts", "../../@dnd-kit/core/src/components/DndContext/DndContext.tsx", "../../@dnd-kit/core/src/hooks/useDraggable.ts", "../../@dnd-kit/core/src/hooks/useDndContext.ts", "../../@dnd-kit/core/src/hooks/useDroppable.ts", "../../@dnd-kit/core/src/components/DragOverlay/components/AnimationManager/AnimationManager.tsx", "../../@dnd-kit/core/src/components/DragOverlay/components/NullifiedContextProvider/NullifiedContextProvider.tsx", "../../@dnd-kit/core/src/components/DragOverlay/components/PositionedOverlay/PositionedOverlay.tsx", "../../@dnd-kit/core/src/components/DragOverlay/hooks/useDropAnimation.ts", "../../@dnd-kit/core/src/components/DragOverlay/hooks/useKey.ts", "../../@dnd-kit/core/src/components/DragOverlay/DragOverlay.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface Props {\n  id: string;\n  value: string;\n}\n\nconst hiddenStyles: React.CSSProperties = {\n  display: 'none',\n};\n\nexport function HiddenText({id, value}: Props) {\n  return (\n    <div id={id} style={hiddenStyles}>\n      {value}\n    </div>\n  );\n}\n", "import React from 'react';\n\nexport interface Props {\n  id: string;\n  announcement: string;\n  ariaLiveType?: \"polite\" | \"assertive\" | \"off\";\n}\n\nexport function LiveRegion({id, announcement, ariaLiveType = \"assertive\"}: Props) {\n  // Hide element visually but keep it readable by screen readers\n  const visuallyHidden: React.CSSProperties = {\n    position: 'fixed',\n    top: 0,\n    left: 0,\n    width: 1,\n    height: 1,\n    margin: -1,\n    border: 0,\n    padding: 0,\n    overflow: 'hidden',\n    clip: 'rect(0 0 0 0)',\n    clipPath: 'inset(100%)',\n    whiteSpace: 'nowrap',\n  };\n  \n  return (\n    <div\n      id={id}\n      style={visuallyHidden}\n      role=\"status\"\n      aria-live={ariaLiveType}\n      aria-atomic\n    >\n      {announcement}\n    </div>\n  );\n}\n", "import {useCallback, useState} from 'react';\n\nexport function useAnnouncement() {\n  const [announcement, setAnnouncement] = useState('');\n  const announce = useCallback((value: string | undefined) => {\n    if (value != null) {\n      setAnnouncement(value);\n    }\n  }, []);\n\n  return {announce, announcement} as const;\n}\n", "import {createContext} from 'react';\n\nimport type {RegisterListener} from './types';\n\nexport const DndMonitorContext = createContext<RegisterListener | null>(null);\n", "import {useContext, useEffect} from 'react';\n\nimport {DndMonitorContext} from './context';\nimport type {DndMonitorListener} from './types';\n\nexport function useDndMonitor(listener: DndMonitorListener) {\n  const registerListener = useContext(DndMonitorContext);\n\n  useEffect(() => {\n    if (!registerListener) {\n      throw new Error(\n        'useDndMonitor must be used within a children of <DndContext>'\n      );\n    }\n\n    const unsubscribe = registerListener(listener);\n\n    return unsubscribe;\n  }, [listener, registerListener]);\n}\n", "import {useCallback, useState} from 'react';\n\nimport type {DndMonitorListener, DndMonitorEvent} from './types';\n\nexport function useDndMonitorProvider() {\n  const [listeners] = useState(() => new Set<DndMonitorListener>());\n\n  const registerListener = useCallback(\n    (listener) => {\n      listeners.add(listener);\n      return () => listeners.delete(listener);\n    },\n    [listeners]\n  );\n\n  const dispatch = useCallback(\n    ({type, event}: DndMonitorEvent) => {\n      listeners.forEach((listener) => listener[type]?.(event as any));\n    },\n    [listeners]\n  );\n\n  return [dispatch, registerListener] as const;\n}\n", "import type {Announcements, ScreenReaderInstructions} from './types';\n\nexport const defaultScreenReaderInstructions: ScreenReaderInstructions = {\n  draggable: `\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  `,\n};\n\nexport const defaultAnnouncements: Announcements = {\n  onDragStart({active}) {\n    return `Picked up draggable item ${active.id}.`;\n  },\n  onDragOver({active, over}) {\n    if (over) {\n      return `Draggable item ${active.id} was moved over droppable area ${over.id}.`;\n    }\n\n    return `Draggable item ${active.id} is no longer over a droppable area.`;\n  },\n  onDragEnd({active, over}) {\n    if (over) {\n      return `Draggable item ${active.id} was dropped over droppable area ${over.id}`;\n    }\n\n    return `Draggable item ${active.id} was dropped.`;\n  },\n  onDragCancel({active}) {\n    return `Dragging was cancelled. Draggable item ${active.id} was dropped.`;\n  },\n};\n", "import React, {useEffect, useMemo, useState} from 'react';\nimport {createPortal} from 'react-dom';\nimport {useUniqueId} from '@dnd-kit/utilities';\nimport {HiddenText, LiveRegion, useAnnouncement} from '@dnd-kit/accessibility';\n\nimport {DndMonitorListener, useDndMonitor} from '../DndMonitor';\n\nimport type {Announcements, ScreenReaderInstructions} from './types';\nimport {\n  defaultAnnouncements,\n  defaultScreenReaderInstructions,\n} from './defaults';\n\ninterface Props {\n  announcements?: Announcements;\n  container?: Element;\n  screenReaderInstructions?: ScreenReaderInstructions;\n  hiddenTextDescribedById: string;\n}\n\nexport function Accessibility({\n  announcements = defaultAnnouncements,\n  container,\n  hiddenTextDescribedById,\n  screenReaderInstructions = defaultScreenReaderInstructions,\n}: Props) {\n  const {announce, announcement} = useAnnouncement();\n  const liveRegionId = useUniqueId(`DndLiveRegion`);\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  useDndMonitor(\n    useMemo<DndMonitorListener>(\n      () => ({\n        onDragStart({active}) {\n          announce(announcements.onDragStart({active}));\n        },\n        onDragMove({active, over}) {\n          if (announcements.onDragMove) {\n            announce(announcements.onDragMove({active, over}));\n          }\n        },\n        onDragOver({active, over}) {\n          announce(announcements.onDragOver({active, over}));\n        },\n        onDragEnd({active, over}) {\n          announce(announcements.onDragEnd({active, over}));\n        },\n        onDragCancel({active, over}) {\n          announce(announcements.onDragCancel({active, over}));\n        },\n      }),\n      [announce, announcements]\n    )\n  );\n\n  if (!mounted) {\n    return null;\n  }\n\n  const markup = (\n    <>\n      <HiddenText\n        id={hiddenTextDescribedById}\n        value={screenReaderInstructions.draggable}\n      />\n      <LiveRegion id={liveRegionId} announcement={announcement} />\n    </>\n  );\n\n  return container ? createPortal(markup, container) : markup;\n}\n", "import type {Coordinates, UniqueIdentifier} from '../types';\nimport type {DroppableContainer} from './types';\n\nexport enum Action {\n  DragStart = 'dragStart',\n  DragMove = 'dragMove',\n  DragEnd = 'dragEnd',\n  DragCancel = 'dragCancel',\n  DragOver = 'dragOver',\n  RegisterDroppable = 'registerDroppable',\n  SetDroppableDisabled = 'setDroppableDisabled',\n  UnregisterDroppable = 'unregisterDroppable',\n}\n\nexport type Actions =\n  | {\n      type: Action.DragStart;\n      active: UniqueIdentifier;\n      initialCoordinates: Coordinates;\n    }\n  | {type: Action.DragMove; coordinates: Coordinates}\n  | {type: Action.DragEnd}\n  | {type: Action.DragCancel}\n  | {\n      type: Action.RegisterDroppable;\n      element: DroppableContainer;\n    }\n  | {\n      type: Action.SetDroppableDisabled;\n      id: UniqueIdentifier;\n      key: UniqueIdentifier;\n      disabled: boolean;\n    }\n  | {\n      type: Action.UnregisterDroppable;\n      id: UniqueIdentifier;\n      key: UniqueIdentifier;\n    };\n", "export function noop(..._args: any) {}\n", "import {useMemo} from 'react';\n\nimport type {Sensor, SensorDescriptor, SensorOptions} from './types';\n\nexport function useSensor<T extends SensorOptions>(\n  sensor: Sensor<T>,\n  options?: T\n): SensorDescriptor<T> {\n  return useMemo(\n    () => ({\n      sensor,\n      options: options ?? ({} as T),\n    }),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [sensor, options]\n  );\n}\n", "import {useMemo} from 'react';\n\nimport type {SensorDescriptor, SensorOptions} from './types';\n\nexport function useSensors(\n  ...sensors: (SensorDescriptor<any> | undefined | null)[]\n): SensorDescriptor<SensorOptions>[] {\n  return useMemo(\n    () =>\n      [...sensors].filter(\n        (sensor): sensor is SensorDescriptor<any> => sensor != null\n      ),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [...sensors]\n  );\n}\n", "import type {Coordinates} from '../../types';\n\nexport const defaultCoordinates: Coordinates = Object.freeze({\n  x: 0,\n  y: 0,\n});\n", "import type {Coordinates} from '../../types';\n\n/**\n * Returns the distance between two points\n */\nexport function distanceBetween(p1: Coordinates, p2: Coordinates) {\n  return Math.sqrt(Math.pow(p1.x - p2.x, 2) + Math.pow(p1.y - p2.y, 2));\n}\n", "import {getEventCoordinates} from '@dnd-kit/utilities';\nimport type {ClientRect} from '../../types';\n\nexport function getRelativeTransformOrigin(\n  event: MouseEvent | TouchEvent | KeyboardEvent,\n  rect: ClientRect\n) {\n  const eventCoordinates = getEventCoordinates(event);\n\n  if (!eventCoordinates) {\n    return '0 0';\n  }\n\n  const transformOrigin = {\n    x: ((eventCoordinates.x - rect.left) / rect.width) * 100,\n    y: ((eventCoordinates.y - rect.top) / rect.height) * 100,\n  };\n\n  return `${transformOrigin.x}% ${transformOrigin.y}%`;\n}\n", "/* eslint-disable no-redeclare */\nimport type {ClientRect} from '../../types';\n\nimport type {Collision, CollisionDescriptor} from './types';\n\n/**\n * Sort collisions from smallest to greatest value\n */\nexport function sortCollisionsAsc(\n  {data: {value: a}}: CollisionDescriptor,\n  {data: {value: b}}: CollisionDescriptor\n) {\n  return a - b;\n}\n\n/**\n * Sort collisions from greatest to smallest value\n */\nexport function sortCollisionsDesc(\n  {data: {value: a}}: CollisionDescriptor,\n  {data: {value: b}}: CollisionDescriptor\n) {\n  return b - a;\n}\n\n/**\n * Returns the coordinates of the corners of a given rectangle:\n * [TopLeft {x, y}, TopRight {x, y}, BottomLeft {x, y}, BottomRight {x, y}]\n */\nexport function cornersOfRectangle({left, top, height, width}: ClientRect) {\n  return [\n    {\n      x: left,\n      y: top,\n    },\n    {\n      x: left + width,\n      y: top,\n    },\n    {\n      x: left,\n      y: top + height,\n    },\n    {\n      x: left + width,\n      y: top + height,\n    },\n  ];\n}\n\n/**\n * Returns the first collision, or null if there isn't one.\n * If a property is specified, returns the specified property of the first collision.\n */\nexport function getFirstCollision(\n  collisions: Collision[] | null | undefined\n): Collision | null;\nexport function getFirstCollision<T extends keyof Collision>(\n  collisions: Collision[] | null | undefined,\n  property: T\n): Collision[T] | null;\nexport function getFirstCollision(\n  collisions: Collision[] | null | undefined,\n  property?: keyof Collision\n) {\n  if (!collisions || collisions.length === 0) {\n    return null;\n  }\n\n  const [firstCollision] = collisions;\n\n  return property ? firstCollision[property] : firstCollision;\n}\n", "import {distanceBetween} from '../coordinates';\nimport type {Coordinates, ClientRect} from '../../types';\n\nimport type {CollisionDescriptor, CollisionDetection} from './types';\nimport {sortCollisionsAsc} from './helpers';\n\n/**\n * Returns the coordinates of the center of a given ClientRect\n */\nfunction centerOfRectangle(\n  rect: ClientRect,\n  left = rect.left,\n  top = rect.top\n): Coordinates {\n  return {\n    x: left + rect.width * 0.5,\n    y: top + rect.height * 0.5,\n  };\n}\n\n/**\n * Returns the closest rectangles from an array of rectangles to the center of a given\n * rectangle.\n */\nexport const closestCenter: CollisionDetection = ({\n  collisionRect,\n  droppableRects,\n  droppableContainers,\n}) => {\n  const centerRect = centerOfRectangle(\n    collisionRect,\n    collisionRect.left,\n    collisionRect.top\n  );\n  const collisions: CollisionDescriptor[] = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {id} = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const distBetween = distanceBetween(centerOfRectangle(rect), centerRect);\n\n      collisions.push({id, data: {droppableContainer, value: distBetween}});\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n", "import {distanceBetween} from '../coordinates';\n\nimport type {CollisionDescriptor, CollisionDetection} from './types';\nimport {cornersOfRectangle, sortCollisionsAsc} from './helpers';\n\n/**\n * Returns the closest rectangles from an array of rectangles to the corners of\n * another rectangle.\n */\nexport const closestCorners: CollisionDetection = ({\n  collisionRect,\n  droppableRects,\n  droppableContainers,\n}) => {\n  const corners = cornersOfRectangle(collisionRect);\n  const collisions: CollisionDescriptor[] = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {id} = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const rectCorners = cornersOfRectangle(rect);\n      const distances = corners.reduce((accumulator, corner, index) => {\n        return accumulator + distanceBetween(rectCorners[index], corner);\n      }, 0);\n      const effectiveDistance = Number((distances / 4).toFixed(4));\n\n      collisions.push({\n        id,\n        data: {droppableContainer, value: effectiveDistance},\n      });\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n", "import type {ClientRect} from '../../types';\n\nimport type {CollisionDescriptor, CollisionDetection} from './types';\nimport {sortCollisionsDesc} from './helpers';\n\n/**\n * Returns the intersecting rectangle area between two rectangles\n */\nexport function getIntersectionRatio(\n  entry: ClientRect,\n  target: ClientRect\n): number {\n  const top = Math.max(target.top, entry.top);\n  const left = Math.max(target.left, entry.left);\n  const right = Math.min(target.left + target.width, entry.left + entry.width);\n  const bottom = Math.min(target.top + target.height, entry.top + entry.height);\n  const width = right - left;\n  const height = bottom - top;\n\n  if (left < right && top < bottom) {\n    const targetArea = target.width * target.height;\n    const entryArea = entry.width * entry.height;\n    const intersectionArea = width * height;\n    const intersectionRatio =\n      intersectionArea / (targetArea + entryArea - intersectionArea);\n\n    return Number(intersectionRatio.toFixed(4));\n  }\n\n  // Rectangles do not overlap, or overlap has an area of zero (edge/corner overlap)\n  return 0;\n}\n\n/**\n * Returns the rectangles that has the greatest intersection area with a given\n * rectangle in an array of rectangles.\n */\nexport const rectIntersection: CollisionDetection = ({\n  collisionRect,\n  droppableRects,\n  droppableContainers,\n}) => {\n  const collisions: CollisionDescriptor[] = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {id} = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const intersectionRatio = getIntersectionRatio(rect, collisionRect);\n\n      if (intersectionRatio > 0) {\n        collisions.push({\n          id,\n          data: {droppableContainer, value: intersectionRatio},\n        });\n      }\n    }\n  }\n\n  return collisions.sort(sortCollisionsDesc);\n};\n", "import type {Coordinates, ClientRect} from '../../types';\nimport {distanceBetween} from '../coordinates';\n\nimport type {CollisionDescriptor, CollisionDetection} from './types';\nimport {cornersOfRectangle, sortCollisionsAsc} from './helpers';\n\n/**\n * Check if a given point is contained within a bounding rectangle\n */\nfunction isPointWithinRect(point: Coordinates, rect: ClientRect): boolean {\n  const {top, left, bottom, right} = rect;\n\n  return (\n    top <= point.y && point.y <= bottom && left <= point.x && point.x <= right\n  );\n}\n\n/**\n * Returns the rectangles that the pointer is hovering over\n */\nexport const pointerWithin: CollisionDetection = ({\n  droppableContainers,\n  droppableRects,\n  pointerCoordinates,\n}) => {\n  if (!pointerCoordinates) {\n    return [];\n  }\n\n  const collisions: CollisionDescriptor[] = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {id} = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect && isPointWithinRect(pointerCoordinates, rect)) {\n      /* There may be more than a single rectangle intersecting\n       * with the pointer coordinates. In order to sort the\n       * colliding rectangles, we measure the distance between\n       * the pointer and the corners of the intersecting rectangle\n       */\n      const corners = cornersOfRectangle(rect);\n      const distances = corners.reduce((accumulator, corner) => {\n        return accumulator + distanceBetween(pointerCoordinates, corner);\n      }, 0);\n      const effectiveDistance = Number((distances / 4).toFixed(4));\n\n      collisions.push({\n        id,\n        data: {droppableContainer, value: effectiveDistance},\n      });\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n", "import type {Transform} from '@dnd-kit/utilities';\nimport type {ClientRect} from '../../types';\n\nexport function adjustScale(\n  transform: Transform,\n  rect1: ClientRect | null,\n  rect2: ClientRect | null\n): Transform {\n  return {\n    ...transform,\n    scaleX: rect1 && rect2 ? rect1.width / rect2.width : 1,\n    scaleY: rect1 && rect2 ? rect1.height / rect2.height : 1,\n  };\n}\n", "import type {Coordinates, ClientRect} from '../../types';\nimport {defaultCoordinates} from '../coordinates';\n\nexport function getRectDelta(\n  rect1: ClientRect | null,\n  rect2: ClientRect | null\n): Coordinates {\n  return rect1 && rect2\n    ? {\n        x: rect1.left - rect2.left,\n        y: rect1.top - rect2.top,\n      }\n    : defaultCoordinates;\n}\n", "import type {Coordinates, ClientRect} from '../../types';\n\nexport function createRectAdjustmentFn(modifier: number) {\n  return function adjustClientRect(\n    rect: ClientRect,\n    ...adjustments: Coordinates[]\n  ): ClientRect {\n    return adjustments.reduce<ClientRect>(\n      (acc, adjustment) => ({\n        ...acc,\n        top: acc.top + modifier * adjustment.y,\n        bottom: acc.bottom + modifier * adjustment.y,\n        left: acc.left + modifier * adjustment.x,\n        right: acc.right + modifier * adjustment.x,\n      }),\n      {...rect}\n    );\n  };\n}\n\nexport const getAdjustedRect = createRectAdjustmentFn(1);\n", "import type {Transform} from '@dnd-kit/utilities';\n\nexport function parseTransform(transform: string): Transform | null {\n  if (transform.startsWith('matrix3d(')) {\n    const transformArray = transform.slice(9, -1).split(/, /);\n\n    return {\n      x: +transformArray[12],\n      y: +transformArray[13],\n      scaleX: +transformArray[0],\n      scaleY: +transformArray[5],\n    };\n  } else if (transform.startsWith('matrix(')) {\n    const transformArray = transform.slice(7, -1).split(/, /);\n\n    return {\n      x: +transformArray[4],\n      y: +transformArray[5],\n      scaleX: +transformArray[0],\n      scaleY: +transformArray[3],\n    };\n  }\n\n  return null;\n}\n", "import type {ClientRect} from '../../types';\n\nimport {parseTransform} from './parseTransform';\n\nexport function inverseTransform(\n  rect: ClientRect,\n  transform: string,\n  transformOrigin: string\n): ClientRect {\n  const parsedTransform = parseTransform(transform);\n\n  if (!parsedTransform) {\n    return rect;\n  }\n\n  const {scaleX, scaleY, x: translateX, y: translateY} = parsedTransform;\n\n  const x = rect.left - translateX - (1 - scaleX) * parseFloat(transformOrigin);\n  const y =\n    rect.top -\n    translateY -\n    (1 - scaleY) *\n      parseFloat(transformOrigin.slice(transformOrigin.indexOf(' ') + 1));\n  const w = scaleX ? rect.width / scaleX : rect.width;\n  const h = scaleY ? rect.height / scaleY : rect.height;\n\n  return {\n    width: w,\n    height: h,\n    top: y,\n    right: x + w,\n    bottom: y + h,\n    left: x,\n  };\n}\n", "import {getWindow} from '@dnd-kit/utilities';\n\nimport type {ClientRect} from '../../types';\nimport {inverseTransform} from '../transform';\n\ninterface Options {\n  ignoreTransform?: boolean;\n}\n\nconst defaultOptions: Options = {ignoreTransform: false};\n\n/**\n * Returns the bounding client rect of an element relative to the viewport.\n */\nexport function getClientRect(\n  element: Element,\n  options: Options = defaultOptions\n) {\n  let rect: ClientRect = element.getBoundingClientRect();\n\n  if (options.ignoreTransform) {\n    const {transform, transformOrigin} =\n      getWindow(element).getComputedStyle(element);\n\n    if (transform) {\n      rect = inverseTransform(rect, transform, transformOrigin);\n    }\n  }\n\n  const {top, left, width, height, bottom, right} = rect;\n\n  return {\n    top,\n    left,\n    width,\n    height,\n    bottom,\n    right,\n  };\n}\n\n/**\n * Returns the bounding client rect of an element relative to the viewport.\n *\n * @remarks\n * The ClientRect returned by this method does not take into account transforms\n * applied to the element it measures.\n *\n */\nexport function getTransformAgnosticClientRect(element: Element): ClientRect {\n  return getClientRect(element, {ignoreTransform: true});\n}\n", "import type {ClientRect} from '../../types';\n\nexport function getWindowClientRect(element: typeof window): ClientRect {\n  const width = element.innerWidth;\n  const height = element.innerHeight;\n\n  return {\n    top: 0,\n    left: 0,\n    right: width,\n    bottom: height,\n    width,\n    height,\n  };\n}\n", "import {getWindow} from '@dnd-kit/utilities';\n\nexport function isFixed(\n  node: HTMLElement,\n  computedStyle: CSSStyleDeclaration = getWindow(node).getComputedStyle(node)\n): boolean {\n  return computedStyle.position === 'fixed';\n}\n", "import {getWindow} from '@dnd-kit/utilities';\n\nexport function isScrollable(\n  element: HTMLElement,\n  computedStyle: CSSStyleDeclaration = getWindow(element).getComputedStyle(\n    element\n  )\n): boolean {\n  const overflowRegex = /(auto|scroll|overlay)/;\n  const properties = ['overflow', 'overflowX', 'overflowY'];\n\n  return properties.some((property) => {\n    const value = computedStyle[property as keyof CSSStyleDeclaration];\n\n    return typeof value === 'string' ? overflowRegex.test(value) : false;\n  });\n}\n", "import {\n  getWindow,\n  isDocument,\n  isHTMLElement,\n  isSVGElement,\n} from '@dnd-kit/utilities';\n\nimport {isFixed} from './isFixed';\nimport {isScrollable} from './isScrollable';\n\nexport function getScrollableAncestors(\n  element: Node | null,\n  limit?: number\n): Element[] {\n  const scrollParents: Element[] = [];\n\n  function findScrollableAncestors(node: Node | null): Element[] {\n    if (limit != null && scrollParents.length >= limit) {\n      return scrollParents;\n    }\n\n    if (!node) {\n      return scrollParents;\n    }\n\n    if (\n      isDocument(node) &&\n      node.scrollingElement != null &&\n      !scrollParents.includes(node.scrollingElement)\n    ) {\n      scrollParents.push(node.scrollingElement);\n\n      return scrollParents;\n    }\n\n    if (!isHTMLElement(node) || isSVGElement(node)) {\n      return scrollParents;\n    }\n\n    if (scrollParents.includes(node)) {\n      return scrollParents;\n    }\n\n    const computedStyle = getWindow(element).getComputedStyle(node);\n\n    if (node !== element) {\n      if (isScrollable(node, computedStyle)) {\n        scrollParents.push(node);\n      }\n    }\n\n    if (isFixed(node, computedStyle)) {\n      return scrollParents;\n    }\n\n    return findScrollableAncestors(node.parentNode);\n  }\n\n  if (!element) {\n    return scrollParents;\n  }\n\n  return findScrollableAncestors(element);\n}\n\nexport function getFirstScrollableAncestor(node: Node | null): Element | null {\n  const [firstScrollableAncestor] = getScrollableAncestors(node, 1);\n\n  return firstScrollableAncestor ?? null;\n}\n", "import {\n  canUseDOM,\n  isHTMLElement,\n  isDocument,\n  getOwnerDocument,\n  isNode,\n  isWindow,\n} from '@dnd-kit/utilities';\n\nexport function getScrollableElement(element: EventTarget | null) {\n  if (!canUseDOM || !element) {\n    return null;\n  }\n\n  if (isWindow(element)) {\n    return element;\n  }\n\n  if (!isNode(element)) {\n    return null;\n  }\n\n  if (\n    isDocument(element) ||\n    element === getOwnerDocument(element).scrollingElement\n  ) {\n    return window;\n  }\n\n  if (isHTMLElement(element)) {\n    return element;\n  }\n\n  return null;\n}\n", "import {isWindow} from '@dnd-kit/utilities';\n\nimport type {Coordinates} from '../../types';\n\nexport function getScrollXCoordinate(element: Element | typeof window): number {\n  if (isWindow(element)) {\n    return element.scrollX;\n  }\n\n  return element.scrollLeft;\n}\n\nexport function getScrollYCoordinate(element: Element | typeof window): number {\n  if (isWindow(element)) {\n    return element.scrollY;\n  }\n\n  return element.scrollTop;\n}\n\nexport function getScrollCoordinates(\n  element: Element | typeof window\n): Coordinates {\n  return {\n    x: getScrollXCoordinate(element),\n    y: getScrollYCoordinate(element),\n  };\n}\n", "export enum Direction {\n  Forward = 1,\n  Backward = -1,\n}\n", "import {canUseDOM} from '@dnd-kit/utilities';\n\nexport function isDocumentScrollingElement(element: Element | null) {\n  if (!canUseDOM || !element) {\n    return false;\n  }\n\n  return element === document.scrollingElement;\n}\n", "import {isDocumentScrollingElement} from './documentScrollingElement';\n\nexport function getScrollPosition(scrollingContainer: Element) {\n  const minScroll = {\n    x: 0,\n    y: 0,\n  };\n  const dimensions = isDocumentScrollingElement(scrollingContainer)\n    ? {\n        height: window.innerHeight,\n        width: window.innerWidth,\n      }\n    : {\n        height: scrollingContainer.clientHeight,\n        width: scrollingContainer.clientWidth,\n      };\n  const maxScroll = {\n    x: scrollingContainer.scrollWidth - dimensions.width,\n    y: scrollingContainer.scrollHeight - dimensions.height,\n  };\n\n  const isTop = scrollingContainer.scrollTop <= minScroll.y;\n  const isLeft = scrollingContainer.scrollLeft <= minScroll.x;\n  const isBottom = scrollingContainer.scrollTop >= maxScroll.y;\n  const isRight = scrollingContainer.scrollLeft >= maxScroll.x;\n\n  return {\n    isTop,\n    isLeft,\n    isBottom,\n    isRight,\n    maxScroll,\n    minScroll,\n  };\n}\n", "import {Direction, ClientRect} from '../../types';\nimport {getScrollPosition} from './getScrollPosition';\n\ninterface PositionalCoordinates\n  extends Pick<ClientRect, 'top' | 'left' | 'right' | 'bottom'> {}\n\nconst defaultThreshold = {\n  x: 0.2,\n  y: 0.2,\n};\n\nexport function getScrollDirectionAndSpeed(\n  scrollContainer: Element,\n  scrollContainerRect: ClientRect,\n  {top, left, right, bottom}: PositionalCoordinates,\n  acceleration = 10,\n  thresholdPercentage = defaultThreshold\n) {\n  const {isTop, isBottom, isLeft, isRight} = getScrollPosition(scrollContainer);\n\n  const direction = {\n    x: 0,\n    y: 0,\n  };\n  const speed = {\n    x: 0,\n    y: 0,\n  };\n  const threshold = {\n    height: scrollContainerRect.height * thresholdPercentage.y,\n    width: scrollContainerRect.width * thresholdPercentage.x,\n  };\n\n  if (!isTop && top <= scrollContainerRect.top + threshold.height) {\n    // Scroll Up\n    direction.y = Direction.Backward;\n    speed.y =\n      acceleration *\n      Math.abs(\n        (scrollContainerRect.top + threshold.height - top) / threshold.height\n      );\n  } else if (\n    !isBottom &&\n    bottom >= scrollContainerRect.bottom - threshold.height\n  ) {\n    // Scroll Down\n    direction.y = Direction.Forward;\n    speed.y =\n      acceleration *\n      Math.abs(\n        (scrollContainerRect.bottom - threshold.height - bottom) /\n          threshold.height\n      );\n  }\n\n  if (!isRight && right >= scrollContainerRect.right - threshold.width) {\n    // Scroll Right\n    direction.x = Direction.Forward;\n    speed.x =\n      acceleration *\n      Math.abs(\n        (scrollContainerRect.right - threshold.width - right) / threshold.width\n      );\n  } else if (!isLeft && left <= scrollContainerRect.left + threshold.width) {\n    // Scroll Left\n    direction.x = Direction.Backward;\n    speed.x =\n      acceleration *\n      Math.abs(\n        (scrollContainerRect.left + threshold.width - left) / threshold.width\n      );\n  }\n\n  return {\n    direction,\n    speed,\n  };\n}\n", "export function getScrollElementRect(element: Element) {\n  if (element === document.scrollingElement) {\n    const {innerWidth, innerHeight} = window;\n\n    return {\n      top: 0,\n      left: 0,\n      right: innerWidth,\n      bottom: innerHeight,\n      width: innerWidth,\n      height: innerHeight,\n    };\n  }\n\n  const {top, left, right, bottom} = element.getBoundingClientRect();\n\n  return {\n    top,\n    left,\n    right,\n    bottom,\n    width: element.clientWidth,\n    height: element.clientHeight,\n  };\n}\n", "import {add} from '@dnd-kit/utilities';\n\nimport type {Coordinates} from '../../types';\nimport {\n  getScrollCoordinates,\n  getScrollXCoordinate,\n  getScrollYCoordinate,\n} from './getScrollCoordinates';\nimport {defaultCoordinates} from '../coordinates';\n\nexport function getScrollOffsets(scrollableAncestors: Element[]): Coordinates {\n  return scrollableAncestors.reduce<Coordinates>((acc, node) => {\n    return add(acc, getScrollCoordinates(node));\n  }, defaultCoordinates);\n}\n\nexport function getScrollXOffset(scrollableAncestors: Element[]): number {\n  return scrollableAncestors.reduce<number>((acc, node) => {\n    return acc + getScrollXCoordinate(node);\n  }, 0);\n}\n\nexport function getScrollYOffset(scrollableAncestors: Element[]): number {\n  return scrollableAncestors.reduce<number>((acc, node) => {\n    return acc + getScrollYCoordinate(node);\n  }, 0);\n}\n", "import type {ClientRect} from '../../types';\nimport {getClientRect} from '../rect/getRect';\nimport {getFirstScrollableAncestor} from './getScrollableAncestors';\n\nexport function scrollIntoViewIfNeeded(\n  element: HTMLElement | null | undefined,\n  measure: (node: HTMLElement) => ClientRect = getClientRect\n) {\n  if (!element) {\n    return;\n  }\n\n  const {top, left, bottom, right} = measure(element);\n  const firstScrollableAncestor = getFirstScrollableAncestor(element);\n\n  if (!firstScrollableAncestor) {\n    return;\n  }\n\n  if (\n    bottom <= 0 ||\n    right <= 0 ||\n    top >= window.innerHeight ||\n    left >= window.innerWidth\n  ) {\n    element.scrollIntoView({\n      block: 'center',\n      inline: 'center',\n    });\n  }\n}\n", "import type {ClientRect} from '../../types/rect';\nimport {\n  getScrollableAncestors,\n  getScrollOffsets,\n  getScrollXOffset,\n  getScrollYOffset,\n} from '../scroll';\n\nconst properties = [\n  ['x', ['left', 'right'], getScrollXOffset],\n  ['y', ['top', 'bottom'], getScrollYOffset],\n] as const;\n\nexport class Rect {\n  constructor(rect: ClientRect, element: Element) {\n    const scrollableAncestors = getScrollableAncestors(element);\n    const scrollOffsets = getScrollOffsets(scrollableAncestors);\n\n    this.rect = {...rect};\n    this.width = rect.width;\n    this.height = rect.height;\n\n    for (const [axis, keys, getScrollOffset] of properties) {\n      for (const key of keys) {\n        Object.defineProperty(this, key, {\n          get: () => {\n            const currentOffsets = getScrollOffset(scrollableAncestors);\n            const scrollOffsetsDeltla = scrollOffsets[axis] - currentOffsets;\n\n            return this.rect[key] + scrollOffsetsDeltla;\n          },\n          enumerable: true,\n        });\n      }\n    }\n\n    Object.defineProperty(this, 'rect', {enumerable: false});\n  }\n\n  private rect: ClientRect;\n\n  public width: number;\n\n  public height: number;\n\n  // The below properties are set by the `Object.defineProperty` calls in the constructor\n  // @ts-ignore\n  public top: number;\n  // @ts-ignore\n  public bottom: number;\n  // @ts-ignore\n  public right: number;\n  // @ts-ignore\n  public left: number;\n}\n", "export class Listeners {\n  private listeners: [\n    string,\n    EventListenerOrEventListenerObject,\n    AddEventListenerOptions | boolean | undefined\n  ][] = [];\n\n  constructor(private target: EventTarget | null) {}\n\n  public add<T extends Event>(\n    eventName: string,\n    handler: (event: T) => void,\n    options?: AddEventListenerOptions | boolean\n  ) {\n    this.target?.addEventListener(eventName, handler as EventListener, options);\n    this.listeners.push([eventName, handler as EventListener, options]);\n  }\n\n  public removeAll = () => {\n    this.listeners.forEach((listener) =>\n      this.target?.removeEventListener(...listener)\n    );\n  };\n}\n", "import {getOwnerDocument, getWindow} from '@dnd-kit/utilities';\n\nexport function getEventListenerTarget(\n  target: EventTarget | null\n): EventTarget | Document {\n  // If the `event.target` element is removed from the document events will still be targeted\n  // at it, and hence won't always bubble up to the window or document anymore.\n  // If there is any risk of an element being removed while it is being dragged,\n  // the best practice is to attach the event listeners directly to the target.\n  // https://developer.mozilla.org/en-US/docs/Web/API/EventTarget\n\n  const {EventTarget} = getWindow(target);\n\n  return target instanceof EventTarget ? target : getOwnerDocument(target);\n}\n", "import type {Coordinates, DistanceMeasurement} from '../../types';\n\nexport function hasExceededDistance(\n  delta: Coordinates,\n  measurement: DistanceMeasurement\n): boolean {\n  const dx = Math.abs(delta.x);\n  const dy = Math.abs(delta.y);\n\n  if (typeof measurement === 'number') {\n    return Math.sqrt(dx ** 2 + dy ** 2) > measurement;\n  }\n\n  if ('x' in measurement && 'y' in measurement) {\n    return dx > measurement.x && dy > measurement.y;\n  }\n\n  if ('x' in measurement) {\n    return dx > measurement.x;\n  }\n\n  if ('y' in measurement) {\n    return dy > measurement.y;\n  }\n\n  return false;\n}\n", "export enum EventName {\n  Click = 'click',\n  DragStart = 'dragstart',\n  Keydown = 'keydown',\n  ContextMenu = 'contextmenu',\n  Resize = 'resize',\n  SelectionChange = 'selectionchange',\n  VisibilityChange = 'visibilitychange',\n}\n\nexport function preventDefault(event: Event) {\n  event.preventDefault();\n}\n\nexport function stopPropagation(event: Event) {\n  event.stopPropagation();\n}\n", "import type {Coordinates, UniqueIdentifier} from '../../types';\nimport type {SensorContext} from '../types';\n\nexport enum KeyboardCode {\n  Space = 'Space',\n  Down = 'ArrowDown',\n  Right = 'ArrowRight',\n  Left = 'ArrowLeft',\n  Up = 'ArrowUp',\n  Esc = 'Escape',\n  Enter = 'Enter',\n  Tab = 'Tab',\n}\n\nexport type KeyboardCodes = {\n  start: KeyboardEvent['code'][];\n  cancel: KeyboardEvent['code'][];\n  end: KeyboardEvent['code'][];\n};\n\nexport type KeyboardCoordinateGetter = (\n  event: KeyboardEvent,\n  args: {\n    active: UniqueIdentifier;\n    currentCoordinates: Coordinates;\n    context: SensorContext;\n  }\n) => Coordinates | void;\n", "import {KeyboardCoordinateGetter, KeyboardCode, KeyboardCodes} from './types';\n\nexport const defaultKeyboardCodes: KeyboardCodes = {\n  start: [KeyboardCode.Space, KeyboardCode.Enter],\n  cancel: [KeyboardCode.Esc],\n  end: [KeyboardCode.Space, KeyboardCode.Enter, KeyboardCode.Tab],\n};\n\nexport const defaultKeyboardCoordinateGetter: KeyboardCoordinateGetter = (\n  event,\n  {currentCoordinates}\n) => {\n  switch (event.code) {\n    case KeyboardCode.Right:\n      return {\n        ...currentCoordinates,\n        x: currentCoordinates.x + 25,\n      };\n    case KeyboardCode.Left:\n      return {\n        ...currentCoordinates,\n        x: currentCoordinates.x - 25,\n      };\n    case KeyboardCode.Down:\n      return {\n        ...currentCoordinates,\n        y: currentCoordinates.y + 25,\n      };\n    case KeyboardCode.Up:\n      return {\n        ...currentCoordinates,\n        y: currentCoordinates.y - 25,\n      };\n  }\n\n  return undefined;\n};\n", "import {\n  add as getAdjustedCoordinates,\n  subtract as getCoordinates<PERSON><PERSON><PERSON>,\n  getOwnerDocument,\n  getWindow,\n  isKeyboardEvent,\n} from '@dnd-kit/utilities';\n\nimport type {Coordinates} from '../../types';\nimport {\n  defaultCoordinates,\n  getScrollPosition,\n  getScrollElementRect,\n} from '../../utilities';\nimport {scrollIntoViewIfNeeded} from '../../utilities/scroll';\nimport {EventName} from '../events';\nimport {Listeners} from '../utilities';\nimport type {\n  Activators,\n  SensorInstance,\n  SensorProps,\n  SensorOptions,\n} from '../types';\n\nimport {KeyboardCoordinateGetter, KeyboardCode, KeyboardCodes} from './types';\nimport {\n  defaultKeyboardCodes,\n  defaultKeyboardCoordinateGetter,\n} from './defaults';\n\nexport interface KeyboardSensorOptions extends SensorOptions {\n  keyboardCodes?: KeyboardCodes;\n  coordinateGetter?: KeyboardCoordinateGetter;\n  scrollBehavior?: ScrollBehavior;\n  onActivation?({event}: {event: KeyboardEvent}): void;\n}\n\nexport type KeyboardSensorProps = SensorProps<KeyboardSensorOptions>;\n\nexport class KeyboardSensor implements SensorInstance {\n  public autoScrollEnabled = false;\n  private referenceCoordinates: Coordinates | undefined;\n  private listeners: Listeners;\n  private windowListeners: Listeners;\n\n  constructor(private props: KeyboardSensorProps) {\n    const {\n      event: {target},\n    } = props;\n\n    this.props = props;\n    this.listeners = new Listeners(getOwnerDocument(target));\n    this.windowListeners = new Listeners(getWindow(target));\n    this.handleKeyDown = this.handleKeyDown.bind(this);\n    this.handleCancel = this.handleCancel.bind(this);\n\n    this.attach();\n  }\n\n  private attach() {\n    this.handleStart();\n\n    this.windowListeners.add(EventName.Resize, this.handleCancel);\n    this.windowListeners.add(EventName.VisibilityChange, this.handleCancel);\n\n    setTimeout(() => this.listeners.add(EventName.Keydown, this.handleKeyDown));\n  }\n\n  private handleStart() {\n    const {activeNode, onStart} = this.props;\n    const node = activeNode.node.current;\n\n    if (node) {\n      scrollIntoViewIfNeeded(node);\n    }\n\n    onStart(defaultCoordinates);\n  }\n\n  private handleKeyDown(event: Event) {\n    if (isKeyboardEvent(event)) {\n      const {active, context, options} = this.props;\n      const {\n        keyboardCodes = defaultKeyboardCodes,\n        coordinateGetter = defaultKeyboardCoordinateGetter,\n        scrollBehavior = 'smooth',\n      } = options;\n      const {code} = event;\n\n      if (keyboardCodes.end.includes(code)) {\n        this.handleEnd(event);\n        return;\n      }\n\n      if (keyboardCodes.cancel.includes(code)) {\n        this.handleCancel(event);\n        return;\n      }\n\n      const {collisionRect} = context.current;\n      const currentCoordinates = collisionRect\n        ? {x: collisionRect.left, y: collisionRect.top}\n        : defaultCoordinates;\n\n      if (!this.referenceCoordinates) {\n        this.referenceCoordinates = currentCoordinates;\n      }\n\n      const newCoordinates = coordinateGetter(event, {\n        active,\n        context: context.current,\n        currentCoordinates,\n      });\n\n      if (newCoordinates) {\n        const coordinatesDelta = getCoordinatesDelta(\n          newCoordinates,\n          currentCoordinates\n        );\n        const scrollDelta = {\n          x: 0,\n          y: 0,\n        };\n        const {scrollableAncestors} = context.current;\n\n        for (const scrollContainer of scrollableAncestors) {\n          const direction = event.code;\n          const {isTop, isRight, isLeft, isBottom, maxScroll, minScroll} =\n            getScrollPosition(scrollContainer);\n          const scrollElementRect = getScrollElementRect(scrollContainer);\n\n          const clampedCoordinates = {\n            x: Math.min(\n              direction === KeyboardCode.Right\n                ? scrollElementRect.right - scrollElementRect.width / 2\n                : scrollElementRect.right,\n              Math.max(\n                direction === KeyboardCode.Right\n                  ? scrollElementRect.left\n                  : scrollElementRect.left + scrollElementRect.width / 2,\n                newCoordinates.x\n              )\n            ),\n            y: Math.min(\n              direction === KeyboardCode.Down\n                ? scrollElementRect.bottom - scrollElementRect.height / 2\n                : scrollElementRect.bottom,\n              Math.max(\n                direction === KeyboardCode.Down\n                  ? scrollElementRect.top\n                  : scrollElementRect.top + scrollElementRect.height / 2,\n                newCoordinates.y\n              )\n            ),\n          };\n\n          const canScrollX =\n            (direction === KeyboardCode.Right && !isRight) ||\n            (direction === KeyboardCode.Left && !isLeft);\n          const canScrollY =\n            (direction === KeyboardCode.Down && !isBottom) ||\n            (direction === KeyboardCode.Up && !isTop);\n\n          if (canScrollX && clampedCoordinates.x !== newCoordinates.x) {\n            const newScrollCoordinates =\n              scrollContainer.scrollLeft + coordinatesDelta.x;\n            const canScrollToNewCoordinates =\n              (direction === KeyboardCode.Right &&\n                newScrollCoordinates <= maxScroll.x) ||\n              (direction === KeyboardCode.Left &&\n                newScrollCoordinates >= minScroll.x);\n\n            if (canScrollToNewCoordinates && !coordinatesDelta.y) {\n              // We don't need to update coordinates, the scroll adjustment alone will trigger\n              // logic to auto-detect the new container we are over\n              scrollContainer.scrollTo({\n                left: newScrollCoordinates,\n                behavior: scrollBehavior,\n              });\n              return;\n            }\n\n            if (canScrollToNewCoordinates) {\n              scrollDelta.x = scrollContainer.scrollLeft - newScrollCoordinates;\n            } else {\n              scrollDelta.x =\n                direction === KeyboardCode.Right\n                  ? scrollContainer.scrollLeft - maxScroll.x\n                  : scrollContainer.scrollLeft - minScroll.x;\n            }\n\n            if (scrollDelta.x) {\n              scrollContainer.scrollBy({\n                left: -scrollDelta.x,\n                behavior: scrollBehavior,\n              });\n            }\n            break;\n          } else if (canScrollY && clampedCoordinates.y !== newCoordinates.y) {\n            const newScrollCoordinates =\n              scrollContainer.scrollTop + coordinatesDelta.y;\n            const canScrollToNewCoordinates =\n              (direction === KeyboardCode.Down &&\n                newScrollCoordinates <= maxScroll.y) ||\n              (direction === KeyboardCode.Up &&\n                newScrollCoordinates >= minScroll.y);\n\n            if (canScrollToNewCoordinates && !coordinatesDelta.x) {\n              // We don't need to update coordinates, the scroll adjustment alone will trigger\n              // logic to auto-detect the new container we are over\n              scrollContainer.scrollTo({\n                top: newScrollCoordinates,\n                behavior: scrollBehavior,\n              });\n              return;\n            }\n\n            if (canScrollToNewCoordinates) {\n              scrollDelta.y = scrollContainer.scrollTop - newScrollCoordinates;\n            } else {\n              scrollDelta.y =\n                direction === KeyboardCode.Down\n                  ? scrollContainer.scrollTop - maxScroll.y\n                  : scrollContainer.scrollTop - minScroll.y;\n            }\n\n            if (scrollDelta.y) {\n              scrollContainer.scrollBy({\n                top: -scrollDelta.y,\n                behavior: scrollBehavior,\n              });\n            }\n\n            break;\n          }\n        }\n\n        this.handleMove(\n          event,\n          getAdjustedCoordinates(\n            getCoordinatesDelta(newCoordinates, this.referenceCoordinates),\n            scrollDelta\n          )\n        );\n      }\n    }\n  }\n\n  private handleMove(event: Event, coordinates: Coordinates) {\n    const {onMove} = this.props;\n\n    event.preventDefault();\n    onMove(coordinates);\n  }\n\n  private handleEnd(event: Event) {\n    const {onEnd} = this.props;\n\n    event.preventDefault();\n    this.detach();\n    onEnd();\n  }\n\n  private handleCancel(event: Event) {\n    const {onCancel} = this.props;\n\n    event.preventDefault();\n    this.detach();\n    onCancel();\n  }\n\n  private detach() {\n    this.listeners.removeAll();\n    this.windowListeners.removeAll();\n  }\n\n  static activators: Activators<KeyboardSensorOptions> = [\n    {\n      eventName: 'onKeyDown' as const,\n      handler: (\n        event: React.KeyboardEvent,\n        {keyboardCodes = defaultKeyboardCodes, onActivation},\n        {active}\n      ) => {\n        const {code} = event.nativeEvent;\n\n        if (keyboardCodes.start.includes(code)) {\n          const activator = active.activatorNode.current;\n\n          if (activator && event.target !== activator) {\n            return false;\n          }\n\n          event.preventDefault();\n\n          onActivation?.({event: event.nativeEvent});\n\n          return true;\n        }\n\n        return false;\n      },\n    },\n  ];\n}\n", "import {\n  subtract as getCoordina<PERSON><PERSON><PERSON><PERSON>,\n  getEventCoordinates,\n  getOwnerDocument,\n  getWindow,\n} from '@dnd-kit/utilities';\n\nimport {defaultCoordinates} from '../../utilities';\nimport {\n  getEventListenerTarget,\n  hasExceededDistance,\n  Listeners,\n} from '../utilities';\nimport {EventName, preventDefault, stopPropagation} from '../events';\nimport {KeyboardCode} from '../keyboard';\nimport type {SensorInstance, SensorProps, SensorOptions} from '../types';\nimport type {Coordinates, DistanceMeasurement} from '../../types';\n\ninterface DistanceConstraint {\n  distance: DistanceMeasurement;\n  tolerance?: DistanceMeasurement;\n}\n\ninterface DelayConstraint {\n  delay: number;\n  tolerance: DistanceMeasurement;\n}\n\ninterface EventDescriptor {\n  name: keyof DocumentEventMap;\n  passive?: boolean;\n}\n\nexport interface PointerEventHandlers {\n  cancel?: EventDescriptor;\n  move: EventDescriptor;\n  end: EventDescriptor;\n}\n\nexport type PointerActivationConstraint =\n  | DelayConstraint\n  | DistanceConstraint\n  | (DelayConstraint & DistanceConstraint);\n\nfunction isDistanceConstraint(\n  constraint: PointerActivationConstraint\n): constraint is PointerActivationConstraint & DistanceConstraint {\n  return Boolean(constraint && 'distance' in constraint);\n}\n\nfunction isDelayConstraint(\n  constraint: PointerActivationConstraint\n): constraint is DelayConstraint {\n  return Boolean(constraint && 'delay' in constraint);\n}\n\nexport interface AbstractPointerSensorOptions extends SensorOptions {\n  activationConstraint?: PointerActivationConstraint;\n  bypassActivationConstraint?(\n    props: Pick<AbstractPointerSensorProps, 'activeNode' | 'event' | 'options'>\n  ): boolean;\n  onActivation?({event}: {event: Event}): void;\n}\n\nexport type AbstractPointerSensorProps =\n  SensorProps<AbstractPointerSensorOptions>;\n\nexport class AbstractPointerSensor implements SensorInstance {\n  public autoScrollEnabled = true;\n  private document: Document;\n  private activated: boolean = false;\n  private initialCoordinates: Coordinates;\n  private timeoutId: NodeJS.Timeout | null = null;\n  private listeners: Listeners;\n  private documentListeners: Listeners;\n  private windowListeners: Listeners;\n\n  constructor(\n    private props: AbstractPointerSensorProps,\n    private events: PointerEventHandlers,\n    listenerTarget = getEventListenerTarget(props.event.target)\n  ) {\n    const {event} = props;\n    const {target} = event;\n\n    this.props = props;\n    this.events = events;\n    this.document = getOwnerDocument(target);\n    this.documentListeners = new Listeners(this.document);\n    this.listeners = new Listeners(listenerTarget);\n    this.windowListeners = new Listeners(getWindow(target));\n    this.initialCoordinates = getEventCoordinates(event) ?? defaultCoordinates;\n    this.handleStart = this.handleStart.bind(this);\n    this.handleMove = this.handleMove.bind(this);\n    this.handleEnd = this.handleEnd.bind(this);\n    this.handleCancel = this.handleCancel.bind(this);\n    this.handleKeydown = this.handleKeydown.bind(this);\n    this.removeTextSelection = this.removeTextSelection.bind(this);\n\n    this.attach();\n  }\n\n  private attach() {\n    const {\n      events,\n      props: {\n        options: {activationConstraint, bypassActivationConstraint},\n      },\n    } = this;\n\n    this.listeners.add(events.move.name, this.handleMove, {passive: false});\n    this.listeners.add(events.end.name, this.handleEnd);\n\n    if (events.cancel) {\n      this.listeners.add(events.cancel.name, this.handleCancel);\n    }\n\n    this.windowListeners.add(EventName.Resize, this.handleCancel);\n    this.windowListeners.add(EventName.DragStart, preventDefault);\n    this.windowListeners.add(EventName.VisibilityChange, this.handleCancel);\n    this.windowListeners.add(EventName.ContextMenu, preventDefault);\n    this.documentListeners.add(EventName.Keydown, this.handleKeydown);\n\n    if (activationConstraint) {\n      if (\n        bypassActivationConstraint?.({\n          event: this.props.event,\n          activeNode: this.props.activeNode,\n          options: this.props.options,\n        })\n      ) {\n        return this.handleStart();\n      }\n\n      if (isDelayConstraint(activationConstraint)) {\n        this.timeoutId = setTimeout(\n          this.handleStart,\n          activationConstraint.delay\n        );\n        this.handlePending(activationConstraint);\n        return;\n      }\n\n      if (isDistanceConstraint(activationConstraint)) {\n        this.handlePending(activationConstraint);\n        return;\n      }\n    }\n\n    this.handleStart();\n  }\n\n  private detach() {\n    this.listeners.removeAll();\n    this.windowListeners.removeAll();\n\n    // Wait until the next event loop before removing document listeners\n    // This is necessary because we listen for `click` and `selection` events on the document\n    setTimeout(this.documentListeners.removeAll, 50);\n\n    if (this.timeoutId !== null) {\n      clearTimeout(this.timeoutId);\n      this.timeoutId = null;\n    }\n  }\n\n  private handlePending(\n    constraint: PointerActivationConstraint,\n    offset?: Coordinates | undefined\n  ): void {\n    const {active, onPending} = this.props;\n    onPending(active, constraint, this.initialCoordinates, offset);\n  }\n\n  private handleStart() {\n    const {initialCoordinates} = this;\n    const {onStart} = this.props;\n\n    if (initialCoordinates) {\n      this.activated = true;\n\n      // Stop propagation of click events once activation constraints are met\n      this.documentListeners.add(EventName.Click, stopPropagation, {\n        capture: true,\n      });\n\n      // Remove any text selection from the document\n      this.removeTextSelection();\n\n      // Prevent further text selection while dragging\n      this.documentListeners.add(\n        EventName.SelectionChange,\n        this.removeTextSelection\n      );\n\n      onStart(initialCoordinates);\n    }\n  }\n\n  private handleMove(event: Event) {\n    const {activated, initialCoordinates, props} = this;\n    const {\n      onMove,\n      options: {activationConstraint},\n    } = props;\n\n    if (!initialCoordinates) {\n      return;\n    }\n\n    const coordinates = getEventCoordinates(event) ?? defaultCoordinates;\n    const delta = getCoordinatesDelta(initialCoordinates, coordinates);\n\n    // Constraint validation\n    if (!activated && activationConstraint) {\n      if (isDistanceConstraint(activationConstraint)) {\n        if (\n          activationConstraint.tolerance != null &&\n          hasExceededDistance(delta, activationConstraint.tolerance)\n        ) {\n          return this.handleCancel();\n        }\n\n        if (hasExceededDistance(delta, activationConstraint.distance)) {\n          return this.handleStart();\n        }\n      }\n\n      if (isDelayConstraint(activationConstraint)) {\n        if (hasExceededDistance(delta, activationConstraint.tolerance)) {\n          return this.handleCancel();\n        }\n      }\n\n      this.handlePending(activationConstraint, delta);\n      return;\n    }\n\n    if (event.cancelable) {\n      event.preventDefault();\n    }\n\n    onMove(coordinates);\n  }\n\n  private handleEnd() {\n    const {onAbort, onEnd} = this.props;\n\n    this.detach();\n    if (!this.activated) {\n      onAbort(this.props.active);\n    }\n    onEnd();\n  }\n\n  private handleCancel() {\n    const {onAbort, onCancel} = this.props;\n\n    this.detach();\n    if (!this.activated) {\n      onAbort(this.props.active);\n    }\n    onCancel();\n  }\n\n  private handleKeydown(event: KeyboardEvent) {\n    if (event.code === KeyboardCode.Esc) {\n      this.handleCancel();\n    }\n  }\n\n  private removeTextSelection() {\n    this.document.getSelection()?.removeAllRanges();\n  }\n}\n", "import type {PointerEvent} from 'react';\nimport {getOwnerDocument} from '@dnd-kit/utilities';\n\nimport type {SensorProps} from '../types';\nimport {\n  AbstractPointerSensor,\n  AbstractPointerSensorOptions,\n  PointerEventHandlers,\n} from './AbstractPointerSensor';\n\nconst events: PointerEventHandlers = {\n  cancel: {name: 'pointercancel'},\n  move: {name: 'pointermove'},\n  end: {name: 'pointerup'},\n};\n\nexport interface PointerSensorOptions extends AbstractPointerSensorOptions {}\n\nexport type PointerSensorProps = SensorProps<PointerSensorOptions>;\n\nexport class PointerSensor extends AbstractPointerSensor {\n  constructor(props: PointerSensorProps) {\n    const {event} = props;\n    // Pointer events stop firing if the target is unmounted while dragging\n    // Therefore we attach listeners to the owner document instead\n    const listenerTarget = getOwnerDocument(event.target);\n\n    super(props, events, listenerTarget);\n  }\n\n  static activators = [\n    {\n      eventName: 'onPointerDown' as const,\n      handler: (\n        {nativeEvent: event}: PointerEvent,\n        {onActivation}: PointerSensorOptions\n      ) => {\n        if (!event.isPrimary || event.button !== 0) {\n          return false;\n        }\n\n        onActivation?.({event});\n\n        return true;\n      },\n    },\n  ];\n}\n", "import type {MouseEvent} from 'react';\nimport {getOwnerDocument} from '@dnd-kit/utilities';\n\nimport type {SensorProps} from '../types';\nimport {\n  AbstractPointerSensor,\n  PointerEventHandlers,\n  AbstractPointerSensorOptions,\n} from '../pointer';\n\nconst events: PointerEventHandlers = {\n  move: {name: 'mousemove'},\n  end: {name: 'mouseup'},\n};\n\nenum MouseButton {\n  RightClick = 2,\n}\n\nexport interface MouseSensorOptions extends AbstractPointerSensorOptions {}\n\nexport type MouseSensorProps = SensorProps<MouseSensorOptions>;\n\nexport class MouseSensor extends AbstractPointerSensor {\n  constructor(props: MouseSensorProps) {\n    super(props, events, getOwnerDocument(props.event.target));\n  }\n\n  static activators = [\n    {\n      eventName: 'onMouseDown' as const,\n      handler: (\n        {nativeEvent: event}: MouseEvent,\n        {onActivation}: MouseSensorOptions\n      ) => {\n        if (event.button === MouseButton.RightClick) {\n          return false;\n        }\n\n        onActivation?.({event});\n\n        return true;\n      },\n    },\n  ];\n}\n", "import type {TouchEvent} from 'react';\n\nimport {\n  AbstractPointerSensor,\n  PointerSensorProps,\n  PointerEventHandlers,\n  PointerSensorOptions,\n} from '../pointer';\nimport type {SensorProps} from '../types';\n\nconst events: PointerEventHandlers = {\n  cancel: {name: 'touchcancel'},\n  move: {name: 'touchmove'},\n  end: {name: 'touchend'},\n};\n\nexport interface TouchSensorOptions extends PointerSensorOptions {}\n\nexport type TouchSensorProps = SensorProps<TouchSensorOptions>;\n\nexport class TouchSensor extends AbstractPointerSensor {\n  constructor(props: PointerSensorProps) {\n    super(props, events);\n  }\n\n  static activators = [\n    {\n      eventName: 'onTouchStart' as const,\n      handler: (\n        {nativeEvent: event}: TouchEvent,\n        {onActivation}: TouchSensorOptions\n      ) => {\n        const {touches} = event;\n\n        if (touches.length > 1) {\n          return false;\n        }\n\n        onActivation?.({event});\n\n        return true;\n      },\n    },\n  ];\n\n  static setup() {\n    // Adding a non-capture and non-passive `touchmove` listener in order\n    // to force `event.preventDefault()` calls to work in dynamically added\n    // touchmove event handlers. This is required for iOS Safari.\n    window.addEventListener(events.move.name, noop, {\n      capture: false,\n      passive: false,\n    });\n\n    return function teardown() {\n      window.removeEventListener(events.move.name, noop);\n    };\n\n    // We create a new handler because the teardown function of another sensor\n    // could remove our event listener if we use a referentially equal listener.\n    function noop() {}\n  }\n}\n", "import {useCallback, useEffect, useMemo, useRef} from 'react';\nimport {useInterval, useLazyMemo, usePrevious} from '@dnd-kit/utilities';\n\nimport {getScrollDirectionAndSpeed} from '../../utilities';\nimport {Direction} from '../../types';\nimport type {Coordinates, ClientRect} from '../../types';\n\nexport type ScrollAncestorSortingFn = (ancestors: Element[]) => Element[];\n\nexport enum AutoScrollActivator {\n  Pointer,\n  DraggableRect,\n}\n\nexport interface Options {\n  acceleration?: number;\n  activator?: AutoScrollActivator;\n  canScroll?: CanScroll;\n  enabled?: boolean;\n  interval?: number;\n  layoutShiftCompensation?:\n    | boolean\n    | {\n        x: boolean;\n        y: boolean;\n      };\n  order?: TraversalOrder;\n  threshold?: {\n    x: number;\n    y: number;\n  };\n}\n\ninterface Arguments extends Options {\n  draggingRect: ClientRect | null;\n  enabled: boolean;\n  pointerCoordinates: Coordinates | null;\n  scrollableAncestors: Element[];\n  scrollableAncestorRects: ClientRect[];\n  delta: Coordinates;\n}\n\nexport type CanScroll = (element: Element) => boolean;\n\nexport enum TraversalOrder {\n  TreeOrder,\n  ReversedTreeOrder,\n}\n\ninterface ScrollDirection {\n  x: 0 | Direction;\n  y: 0 | Direction;\n}\n\nexport function useAutoScroller({\n  acceleration,\n  activator = AutoScrollActivator.Pointer,\n  canScroll,\n  draggingRect,\n  enabled,\n  interval = 5,\n  order = TraversalOrder.TreeOrder,\n  pointerCoordinates,\n  scrollableAncestors,\n  scrollableAncestorRects,\n  delta,\n  threshold,\n}: Arguments) {\n  const scrollIntent = useScrollIntent({delta, disabled: !enabled});\n  const [setAutoScrollInterval, clearAutoScrollInterval] = useInterval();\n  const scrollSpeed = useRef<Coordinates>({x: 0, y: 0});\n  const scrollDirection = useRef<ScrollDirection>({x: 0, y: 0});\n  const rect = useMemo(() => {\n    switch (activator) {\n      case AutoScrollActivator.Pointer:\n        return pointerCoordinates\n          ? {\n              top: pointerCoordinates.y,\n              bottom: pointerCoordinates.y,\n              left: pointerCoordinates.x,\n              right: pointerCoordinates.x,\n            }\n          : null;\n      case AutoScrollActivator.DraggableRect:\n        return draggingRect;\n    }\n  }, [activator, draggingRect, pointerCoordinates]);\n  const scrollContainerRef = useRef<Element | null>(null);\n  const autoScroll = useCallback(() => {\n    const scrollContainer = scrollContainerRef.current;\n\n    if (!scrollContainer) {\n      return;\n    }\n\n    const scrollLeft = scrollSpeed.current.x * scrollDirection.current.x;\n    const scrollTop = scrollSpeed.current.y * scrollDirection.current.y;\n\n    scrollContainer.scrollBy(scrollLeft, scrollTop);\n  }, []);\n  const sortedScrollableAncestors = useMemo(\n    () =>\n      order === TraversalOrder.TreeOrder\n        ? [...scrollableAncestors].reverse()\n        : scrollableAncestors,\n    [order, scrollableAncestors]\n  );\n\n  useEffect(\n    () => {\n      if (!enabled || !scrollableAncestors.length || !rect) {\n        clearAutoScrollInterval();\n        return;\n      }\n\n      for (const scrollContainer of sortedScrollableAncestors) {\n        if (canScroll?.(scrollContainer) === false) {\n          continue;\n        }\n\n        const index = scrollableAncestors.indexOf(scrollContainer);\n        const scrollContainerRect = scrollableAncestorRects[index];\n\n        if (!scrollContainerRect) {\n          continue;\n        }\n\n        const {direction, speed} = getScrollDirectionAndSpeed(\n          scrollContainer,\n          scrollContainerRect,\n          rect,\n          acceleration,\n          threshold\n        );\n\n        for (const axis of ['x', 'y'] as const) {\n          if (!scrollIntent[axis][direction[axis] as Direction]) {\n            speed[axis] = 0;\n            direction[axis] = 0;\n          }\n        }\n\n        if (speed.x > 0 || speed.y > 0) {\n          clearAutoScrollInterval();\n\n          scrollContainerRef.current = scrollContainer;\n          setAutoScrollInterval(autoScroll, interval);\n\n          scrollSpeed.current = speed;\n          scrollDirection.current = direction;\n\n          return;\n        }\n      }\n\n      scrollSpeed.current = {x: 0, y: 0};\n      scrollDirection.current = {x: 0, y: 0};\n      clearAutoScrollInterval();\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n      acceleration,\n      autoScroll,\n      canScroll,\n      clearAutoScrollInterval,\n      enabled,\n      interval,\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      JSON.stringify(rect),\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      JSON.stringify(scrollIntent),\n      setAutoScrollInterval,\n      scrollableAncestors,\n      sortedScrollableAncestors,\n      scrollableAncestorRects,\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      JSON.stringify(threshold),\n    ]\n  );\n}\n\ninterface ScrollIntent {\n  x: Record<Direction, boolean>;\n  y: Record<Direction, boolean>;\n}\n\nconst defaultScrollIntent: ScrollIntent = {\n  x: {[Direction.Backward]: false, [Direction.Forward]: false},\n  y: {[Direction.Backward]: false, [Direction.Forward]: false},\n};\n\nfunction useScrollIntent({\n  delta,\n  disabled,\n}: {\n  delta: Coordinates;\n  disabled: boolean;\n}): ScrollIntent {\n  const previousDelta = usePrevious(delta);\n\n  return useLazyMemo<ScrollIntent>(\n    (previousIntent) => {\n      if (disabled || !previousDelta || !previousIntent) {\n        // Reset scroll intent tracking when auto-scrolling is disabled\n        return defaultScrollIntent;\n      }\n\n      const direction = {\n        x: Math.sign(delta.x - previousDelta.x),\n        y: Math.sign(delta.y - previousDelta.y),\n      };\n\n      // Keep track of the user intent to scroll in each direction for both axis\n      return {\n        x: {\n          [Direction.Backward]:\n            previousIntent.x[Direction.Backward] || direction.x === -1,\n          [Direction.Forward]:\n            previousIntent.x[Direction.Forward] || direction.x === 1,\n        },\n        y: {\n          [Direction.Backward]:\n            previousIntent.y[Direction.Backward] || direction.y === -1,\n          [Direction.Forward]:\n            previousIntent.y[Direction.Forward] || direction.y === 1,\n        },\n      };\n    },\n    [disabled, delta, previousDelta]\n  );\n}\n", "import {useLazyMemo} from '@dnd-kit/utilities';\n\nimport type {DraggableNode, DraggableNodes} from '../../store';\nimport type {UniqueIdentifier} from '../../types';\n\nexport function useCachedNode(\n  draggableNodes: DraggableNodes,\n  id: UniqueIdentifier | null\n): DraggableNode['node']['current'] {\n  const draggableNode = id != null ? draggableNodes.get(id) : undefined;\n  const node = draggableNode ? draggableNode.node.current : null;\n\n  return useLazyMemo(\n    (cachedNode) => {\n      if (id == null) {\n        return null;\n      }\n\n      // In some cases, the draggable node can unmount while dragging\n      // This is the case for virtualized lists. In those situations,\n      // we fall back to the last known value for that node.\n      return node ?? cachedNode ?? null;\n    },\n    [node, id]\n  );\n}\n", "import {useMemo} from 'react';\n\nimport type {SensorActivatorFunction, SensorDescriptor} from '../../sensors';\nimport type {\n  SyntheticListener,\n  SyntheticListeners,\n} from './useSyntheticListeners';\n\nexport function useCombineActivators(\n  sensors: SensorDescriptor<any>[],\n  getSyntheticHandler: (\n    handler: SensorActivatorFunction<any>,\n    sensor: SensorDescriptor<any>\n  ) => SyntheticListener['handler']\n): SyntheticListeners {\n  return useMemo(\n    () =>\n      sensors.reduce<SyntheticListeners>((accumulator, sensor) => {\n        const {sensor: Sensor} = sensor;\n\n        const sensorActivators = Sensor.activators.map((activator) => ({\n          eventName: activator.eventName,\n          handler: getSyntheticHandler(activator.handler, sensor),\n        }));\n\n        return [...accumulator, ...sensorActivators];\n      }, []),\n    [sensors, getSyntheticHandler]\n  );\n}\n", "import {useCallback, useEffect, useRef, useState} from 'react';\nimport {useLatestValue, useLazyMemo} from '@dnd-kit/utilities';\n\nimport {Rect} from '../../utilities/rect';\nimport type {DroppableContainer, RectMap} from '../../store/types';\nimport type {ClientRect, UniqueIdentifier} from '../../types';\n\ninterface Arguments {\n  dragging: boolean;\n  dependencies: any[];\n  config: DroppableMeasuring;\n}\n\nexport enum MeasuringStrategy {\n  Always,\n  BeforeDragging,\n  WhileDragging,\n}\n\nexport enum MeasuringFrequency {\n  Optimized = 'optimized',\n}\n\ntype MeasuringFunction = (element: HTMLElement) => ClientRect;\n\nexport interface DroppableMeasuring {\n  measure: MeasuringFunction;\n  strategy: MeasuringStrategy;\n  frequency: MeasuringFrequency | number;\n}\n\nconst defaultValue: RectMap = new Map();\n\nexport function useDroppableMeasuring(\n  containers: DroppableContainer[],\n  {dragging, dependencies, config}: Arguments\n) {\n  const [queue, setQueue] = useState<UniqueIdentifier[] | null>(null);\n  const {frequency, measure, strategy} = config;\n  const containersRef = useRef(containers);\n  const disabled = isDisabled();\n  const disabledRef = useLatestValue(disabled);\n  const measureDroppableContainers = useCallback(\n    (ids: UniqueIdentifier[] = []) => {\n      if (disabledRef.current) {\n        return;\n      }\n\n      setQueue((value) => {\n        if (value === null) {\n          return ids;\n        }\n\n        return value.concat(ids.filter((id) => !value.includes(id)));\n      });\n    },\n    [disabledRef]\n  );\n  const timeoutId = useRef<NodeJS.Timeout | null>(null);\n  const droppableRects = useLazyMemo<RectMap>(\n    (previousValue) => {\n      if (disabled && !dragging) {\n        return defaultValue;\n      }\n\n      if (\n        !previousValue ||\n        previousValue === defaultValue ||\n        containersRef.current !== containers ||\n        queue != null\n      ) {\n        const map: RectMap = new Map();\n\n        for (let container of containers) {\n          if (!container) {\n            continue;\n          }\n\n          if (\n            queue &&\n            queue.length > 0 &&\n            !queue.includes(container.id) &&\n            container.rect.current\n          ) {\n            // This container does not need to be re-measured\n            map.set(container.id, container.rect.current);\n            continue;\n          }\n\n          const node = container.node.current;\n          const rect = node ? new Rect(measure(node), node) : null;\n\n          container.rect.current = rect;\n\n          if (rect) {\n            map.set(container.id, rect);\n          }\n        }\n\n        return map;\n      }\n\n      return previousValue;\n    },\n    [containers, queue, dragging, disabled, measure]\n  );\n\n  useEffect(() => {\n    containersRef.current = containers;\n  }, [containers]);\n\n  useEffect(\n    () => {\n      if (disabled) {\n        return;\n      }\n\n      measureDroppableContainers();\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [dragging, disabled]\n  );\n\n  useEffect(\n    () => {\n      if (queue && queue.length > 0) {\n        setQueue(null);\n      }\n    },\n    //eslint-disable-next-line react-hooks/exhaustive-deps\n    [JSON.stringify(queue)]\n  );\n\n  useEffect(\n    () => {\n      if (\n        disabled ||\n        typeof frequency !== 'number' ||\n        timeoutId.current !== null\n      ) {\n        return;\n      }\n\n      timeoutId.current = setTimeout(() => {\n        measureDroppableContainers();\n        timeoutId.current = null;\n      }, frequency);\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [frequency, disabled, measureDroppableContainers, ...dependencies]\n  );\n\n  return {\n    droppableRects,\n    measureDroppableContainers,\n    measuringScheduled: queue != null,\n  };\n\n  function isDisabled() {\n    switch (strategy) {\n      case MeasuringStrategy.Always:\n        return false;\n      case MeasuringStrategy.BeforeDragging:\n        return dragging;\n      default:\n        return !dragging;\n    }\n  }\n}\n", "import {useLazyMemo} from '@dnd-kit/utilities';\n\ntype AnyFunction = (...args: any) => any;\n\nexport function useInitialValue<\n  T,\n  U extends AnyFunction | undefined = undefined\n>(\n  value: T | null,\n  computeFn?: U\n): U extends AnyFunction ? ReturnType<U> | null : T | null {\n  return useLazyMemo(\n    (previousValue) => {\n      if (!value) {\n        return null;\n      }\n\n      if (previousValue) {\n        return previousValue;\n      }\n\n      return typeof computeFn === 'function' ? computeFn(value) : value;\n    },\n    [computeFn, value]\n  );\n}\n", "import type {ClientRect} from '../../types';\nimport {useInitialValue} from './useInitialValue';\n\nexport function useInitialRect(\n  node: HTMLElement | null,\n  measure: (node: HTMLElement) => ClientRect\n) {\n  return useInitialValue(node, measure);\n}\n", "import {useEffect, useMemo} from 'react';\nimport {useEvent} from '@dnd-kit/utilities';\n\ninterface Arguments {\n  callback: MutationCallback;\n  disabled?: boolean;\n}\n\n/**\n * Returns a new MutationObserver instance.\n * If `MutationObserver` is undefined in the execution environment, returns `undefined`.\n */\nexport function useMutationObserver({callback, disabled}: Arguments) {\n  const handleMutations = useEvent(callback);\n  const mutationObserver = useMemo(() => {\n    if (\n      disabled ||\n      typeof window === 'undefined' ||\n      typeof window.MutationObserver === 'undefined'\n    ) {\n      return undefined;\n    }\n\n    const {MutationObserver} = window;\n\n    return new MutationObserver(handleMutations);\n  }, [handleMutations, disabled]);\n\n  useEffect(() => {\n    return () => mutationObserver?.disconnect();\n  }, [mutationObserver]);\n\n  return mutationObserver;\n}\n", "import {useEffect, useMemo} from 'react';\nimport {useEvent} from '@dnd-kit/utilities';\n\ninterface Arguments {\n  callback: ResizeObserverCallback;\n  disabled?: boolean;\n}\n\n/**\n * Returns a new ResizeObserver instance bound to the `onResize` callback.\n * If `ResizeObserver` is undefined in the execution environment, returns `undefined`.\n */\nexport function useResizeObserver({callback, disabled}: Arguments) {\n  const handleResize = useEvent(callback);\n  const resizeObserver = useMemo(\n    () => {\n      if (\n        disabled ||\n        typeof window === 'undefined' ||\n        typeof window.ResizeObserver === 'undefined'\n      ) {\n        return undefined;\n      }\n\n      const {ResizeObserver} = window;\n\n      return new ResizeObserver(handleResize);\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [disabled]\n  );\n\n  useEffect(() => {\n    return () => resizeObserver?.disconnect();\n  }, [resizeObserver]);\n\n  return resizeObserver;\n}\n", "import {useState} from 'react';\nimport {useIsomorphicLayoutEffect} from '@dnd-kit/utilities';\n\nimport type {ClientRect} from '../../types';\nimport {getClientRect, Rect} from '../../utilities';\n\nimport {useMutationObserver} from './useMutationObserver';\nimport {useResizeObserver} from './useResizeObserver';\n\nfunction defaultMeasure(element: HTMLElement) {\n  return new Rect(getClientRect(element), element);\n}\n\nexport function useRect(\n  element: HTMLElement | null,\n  measure: (element: HTMLElement) => ClientRect = defaultMeasure,\n  fallbackRect?: ClientRect | null\n) {\n  const [rect, setRect] = useState<ClientRect | null>(null);\n\n  function measureRect() {\n    setRect((currentRect): ClientRect | null => {\n      if (!element) {\n        return null;\n      }\n  \n      if (element.isConnected === false) {\n        // Fall back to last rect we measured if the element is\n        // no longer connected to the DOM.\n        return currentRect ?? fallbackRect ?? null;\n      }\n  \n      const newRect = measure(element);\n  \n      if (JSON.stringify(currentRect) === JSON.stringify(newRect)) {\n        return currentRect;\n      }\n  \n      return newRect;\n    });\n  }\n  \n  const mutationObserver = useMutationObserver({\n    callback(records) {\n      if (!element) {\n        return;\n      }\n\n      for (const record of records) {\n        const {type, target} = record;\n\n        if (\n          type === 'childList' &&\n          target instanceof HTMLElement &&\n          target.contains(element)\n        ) {\n          measureRect();\n          break;\n        }\n      }\n    },\n  });\n  const resizeObserver = useResizeObserver({callback: measureRect});\n\n  useIsomorphicLayoutEffect(() => {\n    measureRect();\n\n    if (element) {\n      resizeObserver?.observe(element);\n      mutationObserver?.observe(document.body, {\n        childList: true,\n        subtree: true,\n      });\n    } else {\n      resizeObserver?.disconnect();\n      mutationObserver?.disconnect();\n    }\n  }, [element]);\n\n  return rect;\n}\n", "import type {ClientRect} from '../../types';\nimport {getRectDelta} from '../../utilities';\n\nimport {useInitialValue} from './useInitialValue';\n\nexport function useRectDelta(rect: ClientRect | null) {\n  const initialRect = useInitialValue(rect);\n\n  return getRectDelta(rect, initialRect);\n}\n", "import {useEffect, useRef} from 'react';\nimport {useLazyMemo} from '@dnd-kit/utilities';\n\nimport {getScrollableAncestors} from '../../utilities';\n\nconst defaultValue: Element[] = [];\n\nexport function useScrollableAncestors(node: HTMLElement | null) {\n  const previousNode = useRef(node);\n\n  const ancestors = useLazyMemo<Element[]>(\n    (previousValue) => {\n      if (!node) {\n        return defaultValue;\n      }\n\n      if (\n        previousValue &&\n        previousValue !== defaultValue &&\n        node &&\n        previousNode.current &&\n        node.parentNode === previousNode.current.parentNode\n      ) {\n        return previousValue;\n      }\n\n      return getScrollableAncestors(node);\n    },\n    [node]\n  );\n\n  useEffect(() => {\n    previousNode.current = node;\n  }, [node]);\n\n  return ancestors;\n}\n", "import {useState, useCallback, useMemo, useRef, useEffect} from 'react';\nimport {add} from '@dnd-kit/utilities';\n\nimport {\n  defaultCoordinates,\n  getScrollableElement,\n  getScrollCoordinates,\n  getScrollOffsets,\n} from '../../utilities';\nimport type {Coordinates} from '../../types';\n\ntype ScrollCoordinates = Map<HTMLElement | Window, Coordinates>;\n\nexport function useScrollOffsets(elements: Element[]): Coordinates {\n  const [\n    scrollCoordinates,\n    setScrollCoordinates,\n  ] = useState<ScrollCoordinates | null>(null);\n  const prevElements = useRef(elements);\n\n  // To-do: Throttle the handleScroll callback\n  const handleScroll = useCallback((event: Event) => {\n    const scrollingElement = getScrollableElement(event.target);\n\n    if (!scrollingElement) {\n      return;\n    }\n\n    setScrollCoordinates((scrollCoordinates) => {\n      if (!scrollCoordinates) {\n        return null;\n      }\n\n      scrollCoordinates.set(\n        scrollingElement,\n        getScrollCoordinates(scrollingElement)\n      );\n\n      return new Map(scrollCoordinates);\n    });\n  }, []);\n\n  useEffect(() => {\n    const previousElements = prevElements.current;\n\n    if (elements !== previousElements) {\n      cleanup(previousElements);\n\n      const entries = elements\n        .map((element) => {\n          const scrollableElement = getScrollableElement(element);\n\n          if (scrollableElement) {\n            scrollableElement.addEventListener('scroll', handleScroll, {\n              passive: true,\n            });\n\n            return [\n              scrollableElement,\n              getScrollCoordinates(scrollableElement),\n            ] as const;\n          }\n\n          return null;\n        })\n        .filter(\n          (\n            entry\n          ): entry is [\n            HTMLElement | (Window & typeof globalThis),\n            Coordinates\n          ] => entry != null\n        );\n\n      setScrollCoordinates(entries.length ? new Map(entries) : null);\n\n      prevElements.current = elements;\n    }\n\n    return () => {\n      cleanup(elements);\n      cleanup(previousElements);\n    };\n\n    function cleanup(elements: Element[]) {\n      elements.forEach((element) => {\n        const scrollableElement = getScrollableElement(element);\n\n        scrollableElement?.removeEventListener('scroll', handleScroll);\n      });\n    }\n  }, [handleScroll, elements]);\n\n  return useMemo(() => {\n    if (elements.length) {\n      return scrollCoordinates\n        ? Array.from(scrollCoordinates.values()).reduce(\n            (acc, coordinates) => add(acc, coordinates),\n            defaultCoordinates\n          )\n        : getScrollOffsets(elements);\n    }\n\n    return defaultCoordinates;\n  }, [elements, scrollCoordinates]);\n}\n", "import {useEffect, useRef} from 'react';\nimport {Coordinates, subtract} from '@dnd-kit/utilities';\n\nimport {defaultCoordinates} from '../../utilities';\n\nexport function useScrollOffsetsDelta(\n  scrollOffsets: Coordinates,\n  dependencies: any[] = []\n) {\n  const initialScrollOffsets = useRef<Coordinates | null>(null);\n\n  useEffect(\n    () => {\n      initialScrollOffsets.current = null;\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    dependencies\n  );\n\n  useEffect(() => {\n    const hasScrollOffsets = scrollOffsets !== defaultCoordinates;\n\n    if (hasScrollOffsets && !initialScrollOffsets.current) {\n      initialScrollOffsets.current = scrollOffsets;\n    }\n\n    if (!hasScrollOffsets && initialScrollOffsets.current) {\n      initialScrollOffsets.current = null;\n    }\n  }, [scrollOffsets]);\n\n  return initialScrollOffsets.current\n    ? subtract(scrollOffsets, initialScrollOffsets.current)\n    : defaultCoordinates;\n}\n", "import {useEffect} from 'react';\nimport {canUseDOM} from '@dnd-kit/utilities';\n\nimport type {SensorDescriptor} from '../../sensors';\n\nexport function useSensorSetup(sensors: SensorDescriptor<any>[]) {\n  useEffect(\n    () => {\n      if (!canUseDOM) {\n        return;\n      }\n\n      const teardownFns = sensors.map(({sensor}) => sensor.setup?.());\n\n      return () => {\n        for (const teardown of teardownFns) {\n          teardown?.();\n        }\n      };\n    },\n    // TO-DO: Sensors length could theoretically change which would not be a valid dependency\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    sensors.map(({sensor}) => sensor)\n  );\n}\n", "import {useMemo} from 'react';\n\nimport type {SyntheticEventName, UniqueIdentifier} from '../../types';\n\nexport type SyntheticListener = {\n  eventName: SyntheticEventName;\n  handler: (event: React.SyntheticEvent, id: UniqueIdentifier) => void;\n};\n\nexport type SyntheticListeners = SyntheticListener[];\n\nexport type SyntheticListenerMap = Record<string, Function>;\n\nexport function useSyntheticListeners(\n  listeners: SyntheticListeners,\n  id: UniqueIdentifier\n): SyntheticListenerMap {\n  return useMemo(() => {\n    return listeners.reduce<SyntheticListenerMap>(\n      (acc, {eventName, handler}) => {\n        acc[eventName] = (event: React.SyntheticEvent) => {\n          handler(event, id);\n        };\n\n        return acc;\n      },\n      {} as SyntheticListenerMap\n    );\n  }, [listeners, id]);\n}\n", "import {useMemo} from 'react';\n\nimport {getWindowClientRect} from '../../utilities/rect';\n\nexport function useWindowRect(element: typeof window | null) {\n  return useMemo(() => (element ? getWindowClientRect(element) : null), [\n    element,\n  ]);\n}\n", "import {useState} from 'react';\nimport {getWindow, useIsomorphicLayoutEffect} from '@dnd-kit/utilities';\n\nimport type {ClientRect} from '../../types';\nimport {Rect, getClientRect} from '../../utilities/rect';\nimport {isDocumentScrollingElement} from '../../utilities';\n\nimport {useResizeObserver} from './useResizeObserver';\nimport {useWindowRect} from './useWindowRect';\n\nconst defaultValue: Rect[] = [];\n\nexport function useRects(\n  elements: Element[],\n  measure: (element: Element) => ClientRect = getClientRect\n): ClientRect[] {\n  const [firstElement] = elements;\n  const windowRect = useWindowRect(\n    firstElement ? getWindow(firstElement) : null\n  );\n  const [rects, setRects] = useState<ClientRect[]>(defaultValue);\n\n  function measureRects() {\n    setRects(() => {\n      if (!elements.length) {\n        return defaultValue;\n      }\n\n      return elements.map((element) =>\n        isDocumentScrollingElement(element)\n          ? (windowRect as ClientRect)\n          : new Rect(measure(element), element)\n      );\n    });\n  }\n\n  const resizeObserver = useResizeObserver({callback: measureRects});\n\n  useIsomorphicLayoutEffect(() => {\n    resizeObserver?.disconnect();\n    measureRects();\n    elements.forEach((element) => resizeObserver?.observe(element));\n  }, [elements]);\n\n  return rects;\n}\n", "import {isHTMLElement} from '@dnd-kit/utilities';\n\nexport function getMeasurableNode(\n  node: HTMLElement | undefined | null\n): HTMLElement | null {\n  if (!node) {\n    return null;\n  }\n\n  if (node.children.length > 1) {\n    return node;\n  }\n  const firstChild = node.children[0];\n\n  return isHTMLElement(firstChild) ? firstChild : node;\n}\n", "import {useMemo, useCallback, useState} from 'react';\nimport {isHTMLElement, useNodeRef} from '@dnd-kit/utilities';\n\nimport {useResizeObserver} from './useResizeObserver';\nimport {getMeasurableNode} from '../../utilities/nodes';\nimport type {PublicContextDescriptor} from '../../store';\nimport type {ClientRect} from '../../types';\n\ninterface Arguments {\n  measure(element: HTMLElement): ClientRect;\n}\n\nexport function useDragOverlayMeasuring({\n  measure,\n}: Arguments): PublicContextDescriptor['dragOverlay'] {\n  const [rect, setRect] = useState<ClientRect | null>(null);\n  const handleResize = useCallback(\n    (entries: ResizeObserverEntry[]) => {\n      for (const {target} of entries) {\n        if (isHTMLElement(target)) {\n          setRect((rect) => {\n            const newRect = measure(target);\n\n            return rect\n              ? {...rect, width: newRect.width, height: newRect.height}\n              : newRect;\n          });\n          break;\n        }\n      }\n    },\n    [measure]\n  );\n  const resizeObserver = useResizeObserver({callback: handleResize});\n  const handleNodeChange = useCallback(\n    (element) => {\n      const node = getMeasurableNode(element);\n\n      resizeObserver?.disconnect();\n\n      if (node) {\n        resizeObserver?.observe(node);\n      }\n\n      setRect(node ? measure(node) : null);\n    },\n    [measure, resizeObserver]\n  );\n  const [nodeRef, setRef] = useNodeRef(handleNodeChange);\n\n  return useMemo(\n    () => ({\n      nodeRef,\n      rect,\n      setRef,\n    }),\n    [rect, nodeRef, setRef]\n  );\n}\n", "import type {DeepRequired} from '@dnd-kit/utilities';\n\nimport type {DataRef} from '../../store/types';\nimport {KeyboardSensor, PointerSensor} from '../../sensors';\nimport {MeasuringStrategy, MeasuringFrequency} from '../../hooks/utilities';\nimport {\n  getClientRect,\n  getTransformAgnosticClientRect,\n} from '../../utilities/rect';\n\nimport type {MeasuringConfiguration} from './types';\n\nexport const defaultSensors = [\n  {sensor: PointerSensor, options: {}},\n  {sensor: KeyboardSensor, options: {}},\n];\n\nexport const defaultData: DataRef = {current: {}};\n\nexport const defaultMeasuringConfiguration: DeepRequired<MeasuringConfiguration> = {\n  draggable: {\n    measure: getTransformAgnosticClientRect,\n  },\n  droppable: {\n    measure: getTransformAgnosticClientRect,\n    strategy: MeasuringStrategy.WhileDragging,\n    frequency: MeasuringFrequency.Optimized,\n  },\n  dragOverlay: {\n    measure: getClientRect,\n  },\n};\n", "import type {UniqueIdentifier} from '../types';\nimport type {DroppableContainer} from './types';\n\ntype Identifier = UniqueIdentifier | null | undefined;\n\nexport class DroppableContainersMap extends Map<\n  UniqueIdentifier,\n  DroppableContainer\n> {\n  get(id: Identifier) {\n    return id != null ? super.get(id) ?? undefined : undefined;\n  }\n\n  toArray(): DroppableContainer[] {\n    return Array.from(this.values());\n  }\n\n  getEnabled(): DroppableContainer[] {\n    return this.toArray().filter(({disabled}) => !disabled);\n  }\n\n  getNodeFor(id: Identifier) {\n    return this.get(id)?.node.current ?? undefined;\n  }\n}\n", "import {createContext} from 'react';\n\nimport {noop} from '../utilities/other';\nimport {defaultMeasuringConfiguration} from '../components/DndContext/defaults';\nimport {DroppableContainersMap} from './constructors';\nimport type {InternalContextDescriptor, PublicContextDescriptor} from './types';\n\nexport const defaultPublicContext: PublicContextDescriptor = {\n  activatorEvent: null,\n  active: null,\n  activeNode: null,\n  activeNodeRect: null,\n  collisions: null,\n  containerNodeRect: null,\n  draggableNodes: new Map(),\n  droppableRects: new Map(),\n  droppableContainers: new DroppableContainersMap(),\n  over: null,\n  dragOverlay: {\n    nodeRef: {\n      current: null,\n    },\n    rect: null,\n    setRef: noop,\n  },\n  scrollableAncestors: [],\n  scrollableAncestorRects: [],\n  measuringConfiguration: defaultMeasuringConfiguration,\n  measureDroppableContainers: noop,\n  windowRect: null,\n  measuringScheduled: false,\n};\n\nexport const defaultInternalContext: InternalContextDescriptor = {\n  activatorEvent: null,\n  activators: [],\n  active: null,\n  activeNodeRect: null,\n  ariaDescribedById: {\n    draggable: '',\n  },\n  dispatch: noop,\n  draggableNodes: new Map(),\n  over: null,\n  measureDroppableContainers: noop,\n};\n\nexport const InternalContext = createContext<InternalContextDescriptor>(\n  defaultInternalContext\n);\n\nexport const PublicContext = createContext<PublicContextDescriptor>(\n  defaultPublicContext\n);\n", "import {Action, Actions} from './actions';\nimport {DroppableContainersMap} from './constructors';\nimport type {State} from './types';\n\nexport function getInitialState(): State {\n  return {\n    draggable: {\n      active: null,\n      initialCoordinates: {x: 0, y: 0},\n      nodes: new Map(),\n      translate: {x: 0, y: 0},\n    },\n    droppable: {\n      containers: new DroppableContainersMap(),\n    },\n  };\n}\n\nexport function reducer(state: State, action: Actions): State {\n  switch (action.type) {\n    case Action.DragStart:\n      return {\n        ...state,\n        draggable: {\n          ...state.draggable,\n          initialCoordinates: action.initialCoordinates,\n          active: action.active,\n        },\n      };\n    case Action.DragMove:\n      if (state.draggable.active == null) {\n        return state;\n      }\n\n      return {\n        ...state,\n        draggable: {\n          ...state.draggable,\n          translate: {\n            x: action.coordinates.x - state.draggable.initialCoordinates.x,\n            y: action.coordinates.y - state.draggable.initialCoordinates.y,\n          },\n        },\n      };\n    case Action.DragEnd:\n    case Action.DragCancel:\n      return {\n        ...state,\n        draggable: {\n          ...state.draggable,\n          active: null,\n          initialCoordinates: {x: 0, y: 0},\n          translate: {x: 0, y: 0},\n        },\n      };\n\n    case Action.RegisterDroppable: {\n      const {element} = action;\n      const {id} = element;\n      const containers = new DroppableContainersMap(state.droppable.containers);\n      containers.set(id, element);\n\n      return {\n        ...state,\n        droppable: {\n          ...state.droppable,\n          containers,\n        },\n      };\n    }\n\n    case Action.SetDroppableDisabled: {\n      const {id, key, disabled} = action;\n      const element = state.droppable.containers.get(id);\n\n      if (!element || key !== element.key) {\n        return state;\n      }\n\n      const containers = new DroppableContainersMap(state.droppable.containers);\n      containers.set(id, {\n        ...element,\n        disabled,\n      });\n\n      return {\n        ...state,\n        droppable: {\n          ...state.droppable,\n          containers,\n        },\n      };\n    }\n\n    case Action.UnregisterDroppable: {\n      const {id, key} = action;\n      const element = state.droppable.containers.get(id);\n\n      if (!element || key !== element.key) {\n        return state;\n      }\n\n      const containers = new DroppableContainersMap(state.droppable.containers);\n      containers.delete(id);\n\n      return {\n        ...state,\n        droppable: {\n          ...state.droppable,\n          containers,\n        },\n      };\n    }\n\n    default: {\n      return state;\n    }\n  }\n}\n", "import {useContext, useEffect} from 'react';\nimport {\n  findFirstFocusableNode,\n  isKeyboardEvent,\n  usePrevious,\n} from '@dnd-kit/utilities';\n\nimport {InternalContext} from '../../../store';\n\ninterface Props {\n  disabled: boolean;\n}\n\nexport function RestoreFocus({disabled}: Props) {\n  const {active, activatorEvent, draggableNodes} = useContext(InternalContext);\n  const previousActivatorEvent = usePrevious(activatorEvent);\n  const previousActiveId = usePrevious(active?.id);\n\n  // Restore keyboard focus on the activator node\n  useEffect(() => {\n    if (disabled) {\n      return;\n    }\n\n    if (!activatorEvent && previousActivatorEvent && previousActiveId != null) {\n      if (!isKeyboardEvent(previousActivatorEvent)) {\n        return;\n      }\n\n      if (document.activeElement === previousActivatorEvent.target) {\n        // No need to restore focus\n        return;\n      }\n\n      const draggableNode = draggableNodes.get(previousActiveId);\n\n      if (!draggableNode) {\n        return;\n      }\n\n      const {activatorNode, node} = draggableNode;\n\n      if (!activatorNode.current && !node.current) {\n        return;\n      }\n\n      requestAnimationFrame(() => {\n        for (const element of [activatorNode.current, node.current]) {\n          if (!element) {\n            continue;\n          }\n\n          const focusableNode = findFirstFocusableNode(element);\n\n          if (focusableNode) {\n            focusableNode.focus();\n            break;\n          }\n        }\n      });\n    }\n  }, [\n    activatorEvent,\n    disabled,\n    draggableNodes,\n    previousActiveId,\n    previousActivatorEvent,\n  ]);\n\n  return null;\n}\n", "import type {FirstArgument, Transform} from '@dnd-kit/utilities';\n\nimport type {Modifiers, Modifier} from './types';\n\nexport function applyModifiers(\n  modifiers: Modifiers | undefined,\n  {transform, ...args}: FirstArgument<Modifier>\n): Transform {\n  return modifiers?.length\n    ? modifiers.reduce<Transform>((accumulator, modifier) => {\n        return modifier({\n          transform: accumulator,\n          ...args,\n        });\n      }, transform)\n    : transform;\n}\n", "import {useMemo} from 'react';\nimport type {DeepRequired} from '@dnd-kit/utilities';\n\nimport {defaultMeasuringConfiguration} from '../defaults';\nimport type {MeasuringConfiguration} from '../types';\n\nexport function useMeasuringConfiguration(\n  config: MeasuringConfiguration | undefined\n): DeepRequired<MeasuringConfiguration> {\n  return useMemo(\n    () => ({\n      draggable: {\n        ...defaultMeasuringConfiguration.draggable,\n        ...config?.draggable,\n      },\n      droppable: {\n        ...defaultMeasuringConfiguration.droppable,\n        ...config?.droppable,\n      },\n      dragOverlay: {\n        ...defaultMeasuringConfiguration.dragOverlay,\n        ...config?.dragOverlay,\n      },\n    }),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [config?.draggable, config?.droppable, config?.dragOverlay]\n  );\n}\n", "import {useRef} from 'react';\nimport {useIsomorphicLayoutEffect} from '@dnd-kit/utilities';\n\nimport {getRectDelta} from '../../../utilities/rect';\nimport {getFirstScrollableAncestor} from '../../../utilities/scroll';\nimport type {ClientRect} from '../../../types';\nimport type {DraggableNode} from '../../../store';\nimport type {MeasuringFunction} from '../types';\n\ninterface Options {\n  activeNode: DraggableNode | null | undefined;\n  config: boolean | {x: boolean; y: boolean} | undefined;\n  initialRect: ClientRect | null;\n  measure: MeasuringFunction;\n}\n\nexport function useLayoutShiftScrollCompensation({\n  activeNode,\n  measure,\n  initialRect,\n  config = true,\n}: Options) {\n  const initialized = useRef(false);\n  const {x, y} = typeof config === 'boolean' ? {x: config, y: config} : config;\n\n  useIsomorphicLayoutEffect(() => {\n    const disabled = !x && !y;\n\n    if (disabled || !activeNode) {\n      initialized.current = false;\n      return;\n    }\n\n    if (initialized.current || !initialRect) {\n      // Return early if layout shift scroll compensation was already attempted\n      // or if there is no initialRect to compare to.\n      return;\n    }\n\n    // Get the most up to date node ref for the active draggable\n    const node = activeNode?.node.current;\n\n    if (!node || node.isConnected === false) {\n      // Return early if there is no attached node ref or if the node is\n      // disconnected from the document.\n      return;\n    }\n\n    const rect = measure(node);\n    const rectDelta = getRectDelta(rect, initialRect);\n\n    if (!x) {\n      rectDelta.x = 0;\n    }\n\n    if (!y) {\n      rectDelta.y = 0;\n    }\n\n    // Only perform layout shift scroll compensation once\n    initialized.current = true;\n\n    if (Math.abs(rectDelta.x) > 0 || Math.abs(rectDelta.y) > 0) {\n      const firstScrollableAncestor = getFirstScrollableAncestor(node);\n\n      if (firstScrollableAncestor) {\n        firstScrollableAncestor.scrollBy({\n          top: rectDelta.y,\n          left: rectDelta.x,\n        });\n      }\n    }\n  }, [activeNode, x, y, initialRect, measure]);\n}\n", "import React, {\n  memo,\n  createContext,\n  useCallback,\n  useEffect,\n  useMemo,\n  useReducer,\n  useRef,\n  useState,\n} from 'react';\nimport {unstable_batchedUpdates} from 'react-dom';\nimport {\n  add,\n  getEventCoordinates,\n  getWindow,\n  useLatestValue,\n  useIsomorphicLayoutEffect,\n  useUniqueId,\n} from '@dnd-kit/utilities';\nimport type {Transform} from '@dnd-kit/utilities';\n\nimport {\n  Action,\n  PublicContext,\n  InternalContext,\n  PublicContextDescriptor,\n  InternalContextDescriptor,\n  getInitialState,\n  reducer,\n} from '../../store';\nimport {DndMonitorContext, useDndMonitorProvider} from '../DndMonitor';\nimport {\n  useAutoScroller,\n  useCachedNode,\n  useCombineActivators,\n  useDragOverlayMeasuring,\n  useDroppableMeasuring,\n  useInitialRect,\n  useRect,\n  useRectDelta,\n  useRects,\n  useScrollableAncestors,\n  useScrollOffsets,\n  useScrollOffsetsDelta,\n  useSensorSetup,\n  useWindowRect,\n} from '../../hooks/utilities';\nimport type {AutoScrollOptions, SyntheticListener} from '../../hooks/utilities';\nimport type {\n  Sensor,\n  SensorContext,\n  SensorDescriptor,\n  SensorActivatorFunction,\n  SensorInstance,\n} from '../../sensors';\nimport {\n  adjustScale,\n  CollisionDetection,\n  defaultCoordinates,\n  getAdjustedRect,\n  getFirstCollision,\n  rectIntersection,\n} from '../../utilities';\nimport {applyModifiers, Modifiers} from '../../modifiers';\nimport type {Active, Over} from '../../store/types';\nimport type {\n  DragStartEvent,\n  DragCancelEvent,\n  DragEndEvent,\n  DragMoveEvent,\n  DragOverEvent,\n  UniqueIdentifier,\n  DragPendingEvent,\n  DragAbortEvent,\n} from '../../types';\nimport {\n  Accessibility,\n  Announcements,\n  RestoreFocus,\n  ScreenReaderInstructions,\n} from '../Accessibility';\n\nimport {defaultData, defaultSensors} from './defaults';\nimport {\n  useLayoutShiftScrollCompensation,\n  useMeasuringConfiguration,\n} from './hooks';\nimport type {MeasuringConfiguration} from './types';\n\nexport interface Props {\n  id?: string;\n  accessibility?: {\n    announcements?: Announcements;\n    container?: Element;\n    restoreFocus?: boolean;\n    screenReaderInstructions?: ScreenReaderInstructions;\n  };\n  autoScroll?: boolean | AutoScrollOptions;\n  cancelDrop?: CancelDrop;\n  children?: React.ReactNode;\n  collisionDetection?: CollisionDetection;\n  measuring?: MeasuringConfiguration;\n  modifiers?: Modifiers;\n  sensors?: SensorDescriptor<any>[];\n  onDragAbort?(event: DragAbortEvent): void;\n  onDragPending?(event: DragPendingEvent): void;\n  onDragStart?(event: DragStartEvent): void;\n  onDragMove?(event: DragMoveEvent): void;\n  onDragOver?(event: DragOverEvent): void;\n  onDragEnd?(event: DragEndEvent): void;\n  onDragCancel?(event: DragCancelEvent): void;\n}\n\nexport interface CancelDropArguments extends DragEndEvent {}\n\nexport type CancelDrop = (\n  args: CancelDropArguments\n) => boolean | Promise<boolean>;\n\ninterface DndEvent extends Event {\n  dndKit?: {\n    capturedBy: Sensor<any>;\n  };\n}\n\nexport const ActiveDraggableContext = createContext<Transform>({\n  ...defaultCoordinates,\n  scaleX: 1,\n  scaleY: 1,\n});\n\nenum Status {\n  Uninitialized,\n  Initializing,\n  Initialized,\n}\n\nexport const DndContext = memo(function DndContext({\n  id,\n  accessibility,\n  autoScroll = true,\n  children,\n  sensors = defaultSensors,\n  collisionDetection = rectIntersection,\n  measuring,\n  modifiers,\n  ...props\n}: Props) {\n  const store = useReducer(reducer, undefined, getInitialState);\n  const [state, dispatch] = store;\n  const [dispatchMonitorEvent, registerMonitorListener] =\n    useDndMonitorProvider();\n  const [status, setStatus] = useState<Status>(Status.Uninitialized);\n  const isInitialized = status === Status.Initialized;\n  const {\n    draggable: {active: activeId, nodes: draggableNodes, translate},\n    droppable: {containers: droppableContainers},\n  } = state;\n  const node = activeId != null ? draggableNodes.get(activeId) : null;\n  const activeRects = useRef<Active['rect']['current']>({\n    initial: null,\n    translated: null,\n  });\n  const active = useMemo<Active | null>(\n    () =>\n      activeId != null\n        ? {\n            id: activeId,\n            // It's possible for the active node to unmount while dragging\n            data: node?.data ?? defaultData,\n            rect: activeRects,\n          }\n        : null,\n    [activeId, node]\n  );\n  const activeRef = useRef<UniqueIdentifier | null>(null);\n  const [activeSensor, setActiveSensor] = useState<SensorInstance | null>(null);\n  const [activatorEvent, setActivatorEvent] = useState<Event | null>(null);\n  const latestProps = useLatestValue(props, Object.values(props));\n  const draggableDescribedById = useUniqueId(`DndDescribedBy`, id);\n  const enabledDroppableContainers = useMemo(\n    () => droppableContainers.getEnabled(),\n    [droppableContainers]\n  );\n  const measuringConfiguration = useMeasuringConfiguration(measuring);\n  const {droppableRects, measureDroppableContainers, measuringScheduled} =\n    useDroppableMeasuring(enabledDroppableContainers, {\n      dragging: isInitialized,\n      dependencies: [translate.x, translate.y],\n      config: measuringConfiguration.droppable,\n    });\n  const activeNode = useCachedNode(draggableNodes, activeId);\n  const activationCoordinates = useMemo(\n    () => (activatorEvent ? getEventCoordinates(activatorEvent) : null),\n    [activatorEvent]\n  );\n  const autoScrollOptions = getAutoScrollerOptions();\n  const initialActiveNodeRect = useInitialRect(\n    activeNode,\n    measuringConfiguration.draggable.measure\n  );\n\n  useLayoutShiftScrollCompensation({\n    activeNode: activeId != null ? draggableNodes.get(activeId) : null,\n    config: autoScrollOptions.layoutShiftCompensation,\n    initialRect: initialActiveNodeRect,\n    measure: measuringConfiguration.draggable.measure,\n  });\n\n  const activeNodeRect = useRect(\n    activeNode,\n    measuringConfiguration.draggable.measure,\n    initialActiveNodeRect\n  );\n  const containerNodeRect = useRect(\n    activeNode ? activeNode.parentElement : null\n  );\n  const sensorContext = useRef<SensorContext>({\n    activatorEvent: null,\n    active: null,\n    activeNode,\n    collisionRect: null,\n    collisions: null,\n    droppableRects,\n    draggableNodes,\n    draggingNode: null,\n    draggingNodeRect: null,\n    droppableContainers,\n    over: null,\n    scrollableAncestors: [],\n    scrollAdjustedTranslate: null,\n  });\n  const overNode = droppableContainers.getNodeFor(\n    sensorContext.current.over?.id\n  );\n  const dragOverlay = useDragOverlayMeasuring({\n    measure: measuringConfiguration.dragOverlay.measure,\n  });\n\n  // Use the rect of the drag overlay if it is mounted\n  const draggingNode = dragOverlay.nodeRef.current ?? activeNode;\n  const draggingNodeRect = isInitialized\n    ? dragOverlay.rect ?? activeNodeRect\n    : null;\n  const usesDragOverlay = Boolean(\n    dragOverlay.nodeRef.current && dragOverlay.rect\n  );\n  // The delta between the previous and new position of the draggable node\n  // is only relevant when there is no drag overlay\n  const nodeRectDelta = useRectDelta(usesDragOverlay ? null : activeNodeRect);\n\n  // Get the window rect of the dragging node\n  const windowRect = useWindowRect(\n    draggingNode ? getWindow(draggingNode) : null\n  );\n\n  // Get scrollable ancestors of the dragging node\n  const scrollableAncestors = useScrollableAncestors(\n    isInitialized ? overNode ?? activeNode : null\n  );\n  const scrollableAncestorRects = useRects(scrollableAncestors);\n\n  // Apply modifiers\n  const modifiedTranslate = applyModifiers(modifiers, {\n    transform: {\n      x: translate.x - nodeRectDelta.x,\n      y: translate.y - nodeRectDelta.y,\n      scaleX: 1,\n      scaleY: 1,\n    },\n    activatorEvent,\n    active,\n    activeNodeRect,\n    containerNodeRect,\n    draggingNodeRect,\n    over: sensorContext.current.over,\n    overlayNodeRect: dragOverlay.rect,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    windowRect,\n  });\n\n  const pointerCoordinates = activationCoordinates\n    ? add(activationCoordinates, translate)\n    : null;\n\n  const scrollOffsets = useScrollOffsets(scrollableAncestors);\n  // Represents the scroll delta since dragging was initiated\n  const scrollAdjustment = useScrollOffsetsDelta(scrollOffsets);\n  // Represents the scroll delta since the last time the active node rect was measured\n  const activeNodeScrollDelta = useScrollOffsetsDelta(scrollOffsets, [\n    activeNodeRect,\n  ]);\n\n  const scrollAdjustedTranslate = add(modifiedTranslate, scrollAdjustment);\n\n  const collisionRect = draggingNodeRect\n    ? getAdjustedRect(draggingNodeRect, modifiedTranslate)\n    : null;\n\n  const collisions =\n    active && collisionRect\n      ? collisionDetection({\n          active,\n          collisionRect,\n          droppableRects,\n          droppableContainers: enabledDroppableContainers,\n          pointerCoordinates,\n        })\n      : null;\n  const overId = getFirstCollision(collisions, 'id');\n  const [over, setOver] = useState<Over | null>(null);\n\n  // When there is no drag overlay used, we need to account for the\n  // window scroll delta\n  const appliedTranslate = usesDragOverlay\n    ? modifiedTranslate\n    : add(modifiedTranslate, activeNodeScrollDelta);\n\n  const transform = adjustScale(\n    appliedTranslate,\n    over?.rect ?? null,\n    activeNodeRect\n  );\n\n  const activeSensorRef = useRef<SensorInstance | null>(null);\n  const instantiateSensor = useCallback(\n    (\n      event: React.SyntheticEvent,\n      {sensor: Sensor, options}: SensorDescriptor<any>\n    ) => {\n      if (activeRef.current == null) {\n        return;\n      }\n\n      const activeNode = draggableNodes.get(activeRef.current);\n\n      if (!activeNode) {\n        return;\n      }\n\n      const activatorEvent = event.nativeEvent;\n\n      const sensorInstance = new Sensor({\n        active: activeRef.current,\n        activeNode,\n        event: activatorEvent,\n        options,\n        // Sensors need to be instantiated with refs for arguments that change over time\n        // otherwise they are frozen in time with the stale arguments\n        context: sensorContext,\n        onAbort(id) {\n          const draggableNode = draggableNodes.get(id);\n\n          if (!draggableNode) {\n            return;\n          }\n\n          const {onDragAbort} = latestProps.current;\n          const event: DragAbortEvent = {id};\n          onDragAbort?.(event);\n          dispatchMonitorEvent({type: 'onDragAbort', event});\n        },\n        onPending(id, constraint, initialCoordinates, offset) {\n          const draggableNode = draggableNodes.get(id);\n\n          if (!draggableNode) {\n            return;\n          }\n\n          const {onDragPending} = latestProps.current;\n          const event: DragPendingEvent = {\n            id,\n            constraint,\n            initialCoordinates,\n            offset,\n          };\n\n          onDragPending?.(event);\n          dispatchMonitorEvent({type: 'onDragPending', event});\n        },\n        onStart(initialCoordinates) {\n          const id = activeRef.current;\n\n          if (id == null) {\n            return;\n          }\n\n          const draggableNode = draggableNodes.get(id);\n\n          if (!draggableNode) {\n            return;\n          }\n\n          const {onDragStart} = latestProps.current;\n          const event: DragStartEvent = {\n            activatorEvent,\n            active: {id, data: draggableNode.data, rect: activeRects},\n          };\n\n          unstable_batchedUpdates(() => {\n            onDragStart?.(event);\n            setStatus(Status.Initializing);\n            dispatch({\n              type: Action.DragStart,\n              initialCoordinates,\n              active: id,\n            });\n            dispatchMonitorEvent({type: 'onDragStart', event});\n            setActiveSensor(activeSensorRef.current);\n            setActivatorEvent(activatorEvent);\n          });\n        },\n        onMove(coordinates) {\n          dispatch({\n            type: Action.DragMove,\n            coordinates,\n          });\n        },\n        onEnd: createHandler(Action.DragEnd),\n        onCancel: createHandler(Action.DragCancel),\n      });\n\n      activeSensorRef.current = sensorInstance;\n\n      function createHandler(type: Action.DragEnd | Action.DragCancel) {\n        return async function handler() {\n          const {active, collisions, over, scrollAdjustedTranslate} =\n            sensorContext.current;\n          let event: DragEndEvent | null = null;\n\n          if (active && scrollAdjustedTranslate) {\n            const {cancelDrop} = latestProps.current;\n\n            event = {\n              activatorEvent,\n              active: active,\n              collisions,\n              delta: scrollAdjustedTranslate,\n              over,\n            };\n\n            if (type === Action.DragEnd && typeof cancelDrop === 'function') {\n              const shouldCancel = await Promise.resolve(cancelDrop(event));\n\n              if (shouldCancel) {\n                type = Action.DragCancel;\n              }\n            }\n          }\n\n          activeRef.current = null;\n\n          unstable_batchedUpdates(() => {\n            dispatch({type});\n            setStatus(Status.Uninitialized);\n            setOver(null);\n            setActiveSensor(null);\n            setActivatorEvent(null);\n            activeSensorRef.current = null;\n\n            const eventName =\n              type === Action.DragEnd ? 'onDragEnd' : 'onDragCancel';\n\n            if (event) {\n              const handler = latestProps.current[eventName];\n\n              handler?.(event);\n              dispatchMonitorEvent({type: eventName, event});\n            }\n          });\n        };\n      }\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [draggableNodes]\n  );\n\n  const bindActivatorToSensorInstantiator = useCallback(\n    (\n      handler: SensorActivatorFunction<any>,\n      sensor: SensorDescriptor<any>\n    ): SyntheticListener['handler'] => {\n      return (event, active) => {\n        const nativeEvent = event.nativeEvent as DndEvent;\n        const activeDraggableNode = draggableNodes.get(active);\n\n        if (\n          // Another sensor is already instantiating\n          activeRef.current !== null ||\n          // No active draggable\n          !activeDraggableNode ||\n          // Event has already been captured\n          nativeEvent.dndKit ||\n          nativeEvent.defaultPrevented\n        ) {\n          return;\n        }\n\n        const activationContext = {\n          active: activeDraggableNode,\n        };\n        const shouldActivate = handler(\n          event,\n          sensor.options,\n          activationContext\n        );\n\n        if (shouldActivate === true) {\n          nativeEvent.dndKit = {\n            capturedBy: sensor.sensor,\n          };\n\n          activeRef.current = active;\n          instantiateSensor(event, sensor);\n        }\n      };\n    },\n    [draggableNodes, instantiateSensor]\n  );\n\n  const activators = useCombineActivators(\n    sensors,\n    bindActivatorToSensorInstantiator\n  );\n\n  useSensorSetup(sensors);\n\n  useIsomorphicLayoutEffect(() => {\n    if (activeNodeRect && status === Status.Initializing) {\n      setStatus(Status.Initialized);\n    }\n  }, [activeNodeRect, status]);\n\n  useEffect(\n    () => {\n      const {onDragMove} = latestProps.current;\n      const {active, activatorEvent, collisions, over} = sensorContext.current;\n\n      if (!active || !activatorEvent) {\n        return;\n      }\n\n      const event: DragMoveEvent = {\n        active,\n        activatorEvent,\n        collisions,\n        delta: {\n          x: scrollAdjustedTranslate.x,\n          y: scrollAdjustedTranslate.y,\n        },\n        over,\n      };\n\n      unstable_batchedUpdates(() => {\n        onDragMove?.(event);\n        dispatchMonitorEvent({type: 'onDragMove', event});\n      });\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [scrollAdjustedTranslate.x, scrollAdjustedTranslate.y]\n  );\n\n  useEffect(\n    () => {\n      const {\n        active,\n        activatorEvent,\n        collisions,\n        droppableContainers,\n        scrollAdjustedTranslate,\n      } = sensorContext.current;\n\n      if (\n        !active ||\n        activeRef.current == null ||\n        !activatorEvent ||\n        !scrollAdjustedTranslate\n      ) {\n        return;\n      }\n\n      const {onDragOver} = latestProps.current;\n      const overContainer = droppableContainers.get(overId);\n      const over =\n        overContainer && overContainer.rect.current\n          ? {\n              id: overContainer.id,\n              rect: overContainer.rect.current,\n              data: overContainer.data,\n              disabled: overContainer.disabled,\n            }\n          : null;\n      const event: DragOverEvent = {\n        active,\n        activatorEvent,\n        collisions,\n        delta: {\n          x: scrollAdjustedTranslate.x,\n          y: scrollAdjustedTranslate.y,\n        },\n        over,\n      };\n\n      unstable_batchedUpdates(() => {\n        setOver(over);\n        onDragOver?.(event);\n        dispatchMonitorEvent({type: 'onDragOver', event});\n      });\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [overId]\n  );\n\n  useIsomorphicLayoutEffect(() => {\n    sensorContext.current = {\n      activatorEvent,\n      active,\n      activeNode,\n      collisionRect,\n      collisions,\n      droppableRects,\n      draggableNodes,\n      draggingNode,\n      draggingNodeRect,\n      droppableContainers,\n      over,\n      scrollableAncestors,\n      scrollAdjustedTranslate,\n    };\n\n    activeRects.current = {\n      initial: draggingNodeRect,\n      translated: collisionRect,\n    };\n  }, [\n    active,\n    activeNode,\n    collisions,\n    collisionRect,\n    draggableNodes,\n    draggingNode,\n    draggingNodeRect,\n    droppableRects,\n    droppableContainers,\n    over,\n    scrollableAncestors,\n    scrollAdjustedTranslate,\n  ]);\n\n  useAutoScroller({\n    ...autoScrollOptions,\n    delta: translate,\n    draggingRect: collisionRect,\n    pointerCoordinates,\n    scrollableAncestors,\n    scrollableAncestorRects,\n  });\n\n  const publicContext = useMemo(() => {\n    const context: PublicContextDescriptor = {\n      active,\n      activeNode,\n      activeNodeRect,\n      activatorEvent,\n      collisions,\n      containerNodeRect,\n      dragOverlay,\n      draggableNodes,\n      droppableContainers,\n      droppableRects,\n      over,\n      measureDroppableContainers,\n      scrollableAncestors,\n      scrollableAncestorRects,\n      measuringConfiguration,\n      measuringScheduled,\n      windowRect,\n    };\n\n    return context;\n  }, [\n    active,\n    activeNode,\n    activeNodeRect,\n    activatorEvent,\n    collisions,\n    containerNodeRect,\n    dragOverlay,\n    draggableNodes,\n    droppableContainers,\n    droppableRects,\n    over,\n    measureDroppableContainers,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    measuringConfiguration,\n    measuringScheduled,\n    windowRect,\n  ]);\n\n  const internalContext = useMemo(() => {\n    const context: InternalContextDescriptor = {\n      activatorEvent,\n      activators,\n      active,\n      activeNodeRect,\n      ariaDescribedById: {\n        draggable: draggableDescribedById,\n      },\n      dispatch,\n      draggableNodes,\n      over,\n      measureDroppableContainers,\n    };\n\n    return context;\n  }, [\n    activatorEvent,\n    activators,\n    active,\n    activeNodeRect,\n    dispatch,\n    draggableDescribedById,\n    draggableNodes,\n    over,\n    measureDroppableContainers,\n  ]);\n\n  return (\n    <DndMonitorContext.Provider value={registerMonitorListener}>\n      <InternalContext.Provider value={internalContext}>\n        <PublicContext.Provider value={publicContext}>\n          <ActiveDraggableContext.Provider value={transform}>\n            {children}\n          </ActiveDraggableContext.Provider>\n        </PublicContext.Provider>\n        <RestoreFocus disabled={accessibility?.restoreFocus === false} />\n      </InternalContext.Provider>\n      <Accessibility\n        {...accessibility}\n        hiddenTextDescribedById={draggableDescribedById}\n      />\n    </DndMonitorContext.Provider>\n  );\n\n  function getAutoScrollerOptions() {\n    const activeSensorDisablesAutoscroll =\n      activeSensor?.autoScrollEnabled === false;\n    const autoScrollGloballyDisabled =\n      typeof autoScroll === 'object'\n        ? autoScroll.enabled === false\n        : autoScroll === false;\n    const enabled =\n      isInitialized &&\n      !activeSensorDisablesAutoscroll &&\n      !autoScrollGloballyDisabled;\n\n    if (typeof autoScroll === 'object') {\n      return {\n        ...autoScroll,\n        enabled,\n      };\n    }\n\n    return {enabled};\n  }\n});\n", "import {createContext, useContext, useMemo} from 'react';\nimport {\n  Transform,\n  useNodeRef,\n  useIsomorphicLayoutEffect,\n  useLatestValue,\n  useUniqueId,\n} from '@dnd-kit/utilities';\n\nimport {InternalContext, Data} from '../store';\nimport type {UniqueIdentifier} from '../types';\nimport {ActiveDraggableContext} from '../components/DndContext';\nimport {useSyntheticListeners, SyntheticListenerMap} from './utilities';\n\nexport interface UseDraggableArguments {\n  id: UniqueIdentifier;\n  data?: Data;\n  disabled?: boolean;\n  attributes?: {\n    role?: string;\n    roleDescription?: string;\n    tabIndex?: number;\n  };\n}\n\nexport interface DraggableAttributes {\n  role: string;\n  tabIndex: number;\n  'aria-disabled': boolean;\n  'aria-pressed': boolean | undefined;\n  'aria-roledescription': string;\n  'aria-describedby': string;\n}\n\nexport type DraggableSyntheticListeners = SyntheticListenerMap | undefined;\n\nconst NullContext = createContext<any>(null);\n\nconst defaultRole = 'button';\n\nconst ID_PREFIX = 'Draggable';\n\nexport function useDraggable({\n  id,\n  data,\n  disabled = false,\n  attributes,\n}: UseDraggableArguments) {\n  const key = useUniqueId(ID_PREFIX);\n  const {\n    activators,\n    activatorEvent,\n    active,\n    activeNodeRect,\n    ariaDescribedById,\n    draggableNodes,\n    over,\n  } = useContext(InternalContext);\n  const {\n    role = defaultRole,\n    roleDescription = 'draggable',\n    tabIndex = 0,\n  } = attributes ?? {};\n  const isDragging = active?.id === id;\n  const transform: Transform | null = useContext(\n    isDragging ? ActiveDraggableContext : NullContext\n  );\n  const [node, setNodeRef] = useNodeRef();\n  const [activatorNode, setActivatorNodeRef] = useNodeRef();\n  const listeners = useSyntheticListeners(activators, id);\n  const dataRef = useLatestValue(data);\n\n  useIsomorphicLayoutEffect(\n    () => {\n      draggableNodes.set(id, {id, key, node, activatorNode, data: dataRef});\n\n      return () => {\n        const node = draggableNodes.get(id);\n\n        if (node && node.key === key) {\n          draggableNodes.delete(id);\n        }\n      };\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [draggableNodes, id]\n  );\n\n  const memoizedAttributes: DraggableAttributes = useMemo(\n    () => ({\n      role,\n      tabIndex,\n      'aria-disabled': disabled,\n      'aria-pressed': isDragging && role === defaultRole ? true : undefined,\n      'aria-roledescription': roleDescription,\n      'aria-describedby': ariaDescribedById.draggable,\n    }),\n    [\n      disabled,\n      role,\n      tabIndex,\n      isDragging,\n      roleDescription,\n      ariaDescribedById.draggable,\n    ]\n  );\n\n  return {\n    active,\n    activatorEvent,\n    activeNodeRect,\n    attributes: memoizedAttributes,\n    isDragging,\n    listeners: disabled ? undefined : listeners,\n    node,\n    over,\n    setNodeRef,\n    setActivatorNodeRef,\n    transform,\n  };\n}\n", "import {ContextType, useContext} from 'react';\nimport {PublicContext} from '../store';\n\nexport function useDndContext() {\n  return useContext(PublicContext);\n}\n\nexport type UseDndContextReturnValue = ContextType<typeof PublicContext>;\n", "import {useCallback, useContext, useEffect, useRef} from 'react';\nimport {useLatestValue, useNodeRef, useUniqueId} from '@dnd-kit/utilities';\n\nimport {InternalContext, Action, Data} from '../store';\nimport type {ClientRect, UniqueIdentifier} from '../types';\n\nimport {useResizeObserver} from './utilities';\n\ninterface ResizeObserverConfig {\n  /** Whether the ResizeObserver should be disabled entirely */\n  disabled?: boolean;\n  /** Resize events may affect the layout and position of other droppable containers.\n   * Specify an array of `UniqueIdentifier` of droppable containers that should also be re-measured\n   * when this droppable container resizes. Specifying an empty array re-measures all droppable containers.\n   */\n  updateMeasurementsFor?: UniqueIdentifier[];\n  /** Represents the debounce timeout between when resize events are observed and when elements are re-measured */\n  timeout?: number;\n}\n\nexport interface UseDroppableArguments {\n  id: UniqueIdentifier;\n  disabled?: boolean;\n  data?: Data;\n  resizeObserverConfig?: ResizeObserverConfig;\n}\n\nconst ID_PREFIX = 'Droppable';\n\nconst defaultResizeObserverConfig = {\n  timeout: 25,\n};\n\nexport function useDroppable({\n  data,\n  disabled = false,\n  id,\n  resizeObserverConfig,\n}: UseDroppableArguments) {\n  const key = useUniqueId(ID_PREFIX);\n  const {active, dispatch, over, measureDroppableContainers} =\n    useContext(InternalContext);\n  const previous = useRef({disabled});\n  const resizeObserverConnected = useRef(false);\n  const rect = useRef<ClientRect | null>(null);\n  const callbackId = useRef<NodeJS.Timeout | null>(null);\n  const {\n    disabled: resizeObserverDisabled,\n    updateMeasurementsFor,\n    timeout: resizeObserverTimeout,\n  } = {\n    ...defaultResizeObserverConfig,\n    ...resizeObserverConfig,\n  };\n  const ids = useLatestValue(updateMeasurementsFor ?? id);\n  const handleResize = useCallback(\n    () => {\n      if (!resizeObserverConnected.current) {\n        // ResizeObserver invokes the `handleResize` callback as soon as `observe` is called,\n        // assuming the element is rendered and displayed.\n        resizeObserverConnected.current = true;\n        return;\n      }\n\n      if (callbackId.current != null) {\n        clearTimeout(callbackId.current);\n      }\n\n      callbackId.current = setTimeout(() => {\n        measureDroppableContainers(\n          Array.isArray(ids.current) ? ids.current : [ids.current]\n        );\n        callbackId.current = null;\n      }, resizeObserverTimeout);\n    },\n    //eslint-disable-next-line react-hooks/exhaustive-deps\n    [resizeObserverTimeout]\n  );\n  const resizeObserver = useResizeObserver({\n    callback: handleResize,\n    disabled: resizeObserverDisabled || !active,\n  });\n  const handleNodeChange = useCallback(\n    (newElement: HTMLElement | null, previousElement: HTMLElement | null) => {\n      if (!resizeObserver) {\n        return;\n      }\n\n      if (previousElement) {\n        resizeObserver.unobserve(previousElement);\n        resizeObserverConnected.current = false;\n      }\n\n      if (newElement) {\n        resizeObserver.observe(newElement);\n      }\n    },\n    [resizeObserver]\n  );\n  const [nodeRef, setNodeRef] = useNodeRef(handleNodeChange);\n  const dataRef = useLatestValue(data);\n\n  useEffect(() => {\n    if (!resizeObserver || !nodeRef.current) {\n      return;\n    }\n\n    resizeObserver.disconnect();\n    resizeObserverConnected.current = false;\n    resizeObserver.observe(nodeRef.current);\n  }, [nodeRef, resizeObserver]);\n\n  useEffect(\n    () => {\n      dispatch({\n        type: Action.RegisterDroppable,\n        element: {\n          id,\n          key,\n          disabled,\n          node: nodeRef,\n          rect,\n          data: dataRef,\n        },\n      });\n\n      return () =>\n        dispatch({\n          type: Action.UnregisterDroppable,\n          key,\n          id,\n        });\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [id]\n  );\n\n  useEffect(() => {\n    if (disabled !== previous.current.disabled) {\n      dispatch({\n        type: Action.SetDroppableDisabled,\n        id,\n        key,\n        disabled,\n      });\n\n      previous.current.disabled = disabled;\n    }\n  }, [id, key, disabled, dispatch]);\n\n  return {\n    active,\n    rect,\n    isOver: over?.id === id,\n    node: nodeRef,\n    over,\n    setNodeRef,\n  };\n}\n", "import React, {cloneElement, useState} from 'react';\nimport {useIsomorphicLayoutEffect, usePrevious} from '@dnd-kit/utilities';\n\nimport type {UniqueIdentifier} from '../../../../types';\n\nexport type Animation = (\n  key: UniqueIdentifier,\n  node: HTMLElement\n) => Promise<void> | void;\n\nexport interface Props {\n  animation: Animation;\n  children: React.ReactElement | null;\n}\n\nexport function AnimationManager({animation, children}: Props) {\n  const [\n    clonedChildren,\n    setClonedChildren,\n  ] = useState<React.ReactElement | null>(null);\n  const [element, setElement] = useState<HTMLElement | null>(null);\n  const previousChildren = usePrevious(children);\n\n  if (!children && !clonedChildren && previousChildren) {\n    setClonedChildren(previousChildren);\n  }\n\n  useIsomorphicLayoutEffect(() => {\n    if (!element) {\n      return;\n    }\n\n    const key = clonedChildren?.key;\n    const id = clonedChildren?.props.id;\n\n    if (key == null || id == null) {\n      setClonedChildren(null);\n      return;\n    }\n\n    Promise.resolve(animation(id, element)).then(() => {\n      setClonedChildren(null);\n    });\n  }, [animation, clonedChildren, element]);\n\n  return (\n    <>\n      {children}\n      {clonedChildren ? cloneElement(clonedChildren, {ref: setElement}) : null}\n    </>\n  );\n}\n", "import React from 'react';\nimport type {Transform} from '@dnd-kit/utilities';\n\nimport {InternalContext, defaultInternalContext} from '../../../../store';\nimport {ActiveDraggableContext} from '../../../DndContext';\n\ninterface Props {\n  children: React.ReactNode;\n}\n\nconst defaultTransform: Transform = {\n  x: 0,\n  y: 0,\n  scaleX: 1,\n  scaleY: 1,\n};\n\nexport function NullifiedContextProvider({children}: Props) {\n  return (\n    <InternalContext.Provider value={defaultInternalContext}>\n      <ActiveDraggableContext.Provider value={defaultTransform}>\n        {children}\n      </ActiveDraggableContext.Provider>\n    </InternalContext.Provider>\n  );\n}\n", "import React, {forwardRef} from 'react';\nimport {CSS, isKeyboardEvent} from '@dnd-kit/utilities';\n\nimport type {Transform} from '@dnd-kit/utilities';\n\nimport {getRelativeTransformOrigin} from '../../../../utilities';\nimport type {ClientRect, UniqueIdentifier} from '../../../../types';\n\ntype TransitionGetter = (\n  activatorEvent: Event | null\n) => React.CSSProperties['transition'] | undefined;\n\nexport interface Props {\n  as: keyof JSX.IntrinsicElements;\n  activatorEvent: Event | null;\n  adjustScale?: boolean;\n  children?: React.ReactNode;\n  className?: string;\n  id: UniqueIdentifier;\n  rect: ClientRect | null;\n  style?: React.CSSProperties;\n  transition?: string | TransitionGetter;\n  transform: Transform;\n}\n\nconst baseStyles: React.CSSProperties = {\n  position: 'fixed',\n  touchAction: 'none',\n};\n\nconst defaultTransition: TransitionGetter = (activatorEvent) => {\n  const isKeyboardActivator = isKeyboardEvent(activatorEvent);\n\n  return isKeyboardActivator ? 'transform 250ms ease' : undefined;\n};\n\nexport const PositionedOverlay = forwardRef<HTMLElement, Props>(\n  (\n    {\n      as,\n      activatorEvent,\n      adjustScale,\n      children,\n      className,\n      rect,\n      style,\n      transform,\n      transition = defaultTransition,\n    },\n    ref\n  ) => {\n    if (!rect) {\n      return null;\n    }\n\n    const scaleAdjustedTransform = adjustScale\n      ? transform\n      : {\n          ...transform,\n          scaleX: 1,\n          scaleY: 1,\n        };\n    const styles: React.CSSProperties | undefined = {\n      ...baseStyles,\n      width: rect.width,\n      height: rect.height,\n      top: rect.top,\n      left: rect.left,\n      transform: CSS.Transform.toString(scaleAdjustedTransform),\n      transformOrigin:\n        adjustScale && activatorEvent\n          ? getRelativeTransformOrigin(\n              activatorEvent as MouseEvent | KeyboardEvent | TouchEvent,\n              rect\n            )\n          : undefined,\n      transition:\n        typeof transition === 'function'\n          ? transition(activatorEvent)\n          : transition,\n      ...style,\n    };\n\n    return React.createElement(\n      as,\n      {\n        className,\n        style: styles,\n        ref,\n      },\n      children\n    );\n  }\n);\n", "import {CSS, useEvent, getWindow} from '@dnd-kit/utilities';\nimport type {DeepRequired, Transform} from '@dnd-kit/utilities';\n\nimport type {\n  Active,\n  DraggableNode,\n  DraggableNodes,\n  DroppableContainers,\n} from '../../../store';\nimport type {ClientRect, UniqueIdentifier} from '../../../types';\nimport {getMeasurableNode} from '../../../utilities/nodes';\nimport {scrollIntoViewIfNeeded} from '../../../utilities/scroll';\nimport {parseTransform} from '../../../utilities/transform';\nimport type {MeasuringConfiguration} from '../../DndContext';\nimport type {Animation} from '../components';\n\ninterface SharedParameters {\n  active: {\n    id: UniqueIdentifier;\n    data: Active['data'];\n    node: HTMLElement;\n    rect: ClientRect;\n  };\n  dragOverlay: {\n    node: HTMLElement;\n    rect: ClientRect;\n  };\n  draggableNodes: DraggableNodes;\n  droppableContainers: DroppableContainers;\n  measuringConfiguration: DeepRequired<MeasuringConfiguration>;\n}\n\nexport interface KeyframeResolverParameters extends SharedParameters {\n  transform: {\n    initial: Transform;\n    final: Transform;\n  };\n}\n\nexport type KeyframeResolver = (\n  parameters: KeyframeResolverParameters\n) => Keyframe[];\n\nexport interface DropAnimationOptions {\n  keyframes?: KeyframeResolver;\n  duration?: number;\n  easing?: string;\n  sideEffects?: DropAnimationSideEffects | null;\n}\n\nexport type DropAnimation = DropAnimationFunction | DropAnimationOptions;\n\ninterface Arguments {\n  draggableNodes: DraggableNodes;\n  droppableContainers: DroppableContainers;\n  measuringConfiguration: DeepRequired<MeasuringConfiguration>;\n  config?: DropAnimation | null;\n}\n\nexport interface DropAnimationFunctionArguments extends SharedParameters {\n  transform: Transform;\n}\n\nexport type DropAnimationFunction = (\n  args: DropAnimationFunctionArguments\n) => Promise<void> | void;\n\ntype CleanupFunction = () => void;\n\nexport interface DropAnimationSideEffectsParameters extends SharedParameters {}\n\nexport type DropAnimationSideEffects = (\n  parameters: DropAnimationSideEffectsParameters\n) => CleanupFunction | void;\n\ntype ExtractStringProperties<T> = {\n  [K in keyof T]?: T[K] extends string ? string : never;\n};\n\ntype Styles = ExtractStringProperties<CSSStyleDeclaration>;\n\ninterface DefaultDropAnimationSideEffectsOptions {\n  className?: {\n    active?: string;\n    dragOverlay?: string;\n  };\n  styles?: {\n    active?: Styles;\n    dragOverlay?: Styles;\n  };\n}\n\nexport const defaultDropAnimationSideEffects = (\n  options: DefaultDropAnimationSideEffectsOptions\n): DropAnimationSideEffects => ({active, dragOverlay}) => {\n  const originalStyles: Record<string, string> = {};\n  const {styles, className} = options;\n\n  if (styles?.active) {\n    for (const [key, value] of Object.entries(styles.active)) {\n      if (value === undefined) {\n        continue;\n      }\n\n      originalStyles[key] = active.node.style.getPropertyValue(key);\n      active.node.style.setProperty(key, value);\n    }\n  }\n\n  if (styles?.dragOverlay) {\n    for (const [key, value] of Object.entries(styles.dragOverlay)) {\n      if (value === undefined) {\n        continue;\n      }\n\n      dragOverlay.node.style.setProperty(key, value);\n    }\n  }\n\n  if (className?.active) {\n    active.node.classList.add(className.active);\n  }\n\n  if (className?.dragOverlay) {\n    dragOverlay.node.classList.add(className.dragOverlay);\n  }\n\n  return function cleanup() {\n    for (const [key, value] of Object.entries(originalStyles)) {\n      active.node.style.setProperty(key, value);\n    }\n\n    if (className?.active) {\n      active.node.classList.remove(className.active);\n    }\n  };\n};\n\nconst defaultKeyframeResolver: KeyframeResolver = ({\n  transform: {initial, final},\n}) => [\n  {\n    transform: CSS.Transform.toString(initial),\n  },\n  {\n    transform: CSS.Transform.toString(final),\n  },\n];\n\nexport const defaultDropAnimationConfiguration: Required<DropAnimationOptions> = {\n  duration: 250,\n  easing: 'ease',\n  keyframes: defaultKeyframeResolver,\n  sideEffects: defaultDropAnimationSideEffects({\n    styles: {\n      active: {\n        opacity: '0',\n      },\n    },\n  }),\n};\n\nexport function useDropAnimation({\n  config,\n  draggableNodes,\n  droppableContainers,\n  measuringConfiguration,\n}: Arguments) {\n  return useEvent<Animation>((id, node) => {\n    if (config === null) {\n      return;\n    }\n\n    const activeDraggable: DraggableNode | undefined = draggableNodes.get(id);\n\n    if (!activeDraggable) {\n      return;\n    }\n\n    const activeNode = activeDraggable.node.current;\n\n    if (!activeNode) {\n      return;\n    }\n\n    const measurableNode = getMeasurableNode(node);\n\n    if (!measurableNode) {\n      return;\n    }\n    const {transform} = getWindow(node).getComputedStyle(node);\n    const parsedTransform = parseTransform(transform);\n\n    if (!parsedTransform) {\n      return;\n    }\n\n    const animation: DropAnimationFunction =\n      typeof config === 'function'\n        ? config\n        : createDefaultDropAnimation(config);\n\n    scrollIntoViewIfNeeded(\n      activeNode,\n      measuringConfiguration.draggable.measure\n    );\n\n    return animation({\n      active: {\n        id,\n        data: activeDraggable.data,\n        node: activeNode,\n        rect: measuringConfiguration.draggable.measure(activeNode),\n      },\n      draggableNodes,\n      dragOverlay: {\n        node,\n        rect: measuringConfiguration.dragOverlay.measure(measurableNode),\n      },\n      droppableContainers,\n      measuringConfiguration,\n      transform: parsedTransform,\n    });\n  });\n}\n\nfunction createDefaultDropAnimation(\n  options: DropAnimationOptions | undefined\n): DropAnimationFunction {\n  const {duration, easing, sideEffects, keyframes} = {\n    ...defaultDropAnimationConfiguration,\n    ...options,\n  };\n\n  return ({active, dragOverlay, transform, ...rest}) => {\n    if (!duration) {\n      // Do not animate if animation duration is zero.\n      return;\n    }\n\n    const delta = {\n      x: dragOverlay.rect.left - active.rect.left,\n      y: dragOverlay.rect.top - active.rect.top,\n    };\n\n    const scale = {\n      scaleX:\n        transform.scaleX !== 1\n          ? (active.rect.width * transform.scaleX) / dragOverlay.rect.width\n          : 1,\n      scaleY:\n        transform.scaleY !== 1\n          ? (active.rect.height * transform.scaleY) / dragOverlay.rect.height\n          : 1,\n    };\n    const finalTransform = {\n      x: transform.x - delta.x,\n      y: transform.y - delta.y,\n      ...scale,\n    };\n\n    const animationKeyframes = keyframes({\n      ...rest,\n      active,\n      dragOverlay,\n      transform: {initial: transform, final: finalTransform},\n    });\n\n    const [firstKeyframe] = animationKeyframes;\n    const lastKeyframe = animationKeyframes[animationKeyframes.length - 1];\n\n    if (JSON.stringify(firstKeyframe) === JSON.stringify(lastKeyframe)) {\n      // The start and end keyframes are the same, infer that there is no animation needed.\n      return;\n    }\n\n    const cleanup = sideEffects?.({active, dragOverlay, ...rest});\n    const animation = dragOverlay.node.animate(animationKeyframes, {\n      duration,\n      easing,\n      fill: 'forwards',\n    });\n\n    return new Promise((resolve) => {\n      animation.onfinish = () => {\n        cleanup?.();\n        resolve();\n      };\n    });\n  };\n}\n", "import {useMemo} from 'react';\n\nimport type {UniqueIdentifier} from '../../../types';\n\nlet key = 0;\n\nexport function useKey(id: UniqueIdentifier | undefined) {\n  return useMemo(() => {\n    if (id == null) {\n      return;\n    }\n\n    key++;\n    return key;\n  }, [id]);\n}\n", "import React, {useContext} from 'react';\n\nimport {applyModifiers, Modifiers} from '../../modifiers';\nimport {ActiveDraggableContext} from '../DndContext';\nimport {useDndContext} from '../../hooks';\nimport {useInitialValue} from '../../hooks/utilities';\n\nimport {\n  AnimationManager,\n  NullifiedContextProvider,\n  PositionedOverlay,\n} from './components';\nimport type {PositionedOverlayProps} from './components';\n\nimport {useDropAnimation, useKey} from './hooks';\nimport type {DropAnimation} from './hooks';\n\nexport interface Props\n  extends Pick<\n    PositionedOverlayProps,\n    'adjustScale' | 'children' | 'className' | 'style' | 'transition'\n  > {\n  dropAnimation?: DropAnimation | null | undefined;\n  modifiers?: Modifiers;\n  wrapperElement?: keyof JSX.IntrinsicElements;\n  zIndex?: number;\n}\n\nexport const DragOverlay = React.memo(\n  ({\n    adjustScale = false,\n    children,\n    dropAnimation: dropAnimationConfig,\n    style,\n    transition,\n    modifiers,\n    wrapperElement = 'div',\n    className,\n    zIndex = 999,\n  }: Props) => {\n    const {\n      activatorEvent,\n      active,\n      activeNodeRect,\n      containerNodeRect,\n      draggableNodes,\n      droppableContainers,\n      dragOverlay,\n      over,\n      measuringConfiguration,\n      scrollableAncestors,\n      scrollableAncestorRects,\n      windowRect,\n    } = useDndContext();\n    const transform = useContext(ActiveDraggableContext);\n    const key = useKey(active?.id);\n    const modifiedTransform = applyModifiers(modifiers, {\n      activatorEvent,\n      active,\n      activeNodeRect,\n      containerNodeRect,\n      draggingNodeRect: dragOverlay.rect,\n      over,\n      overlayNodeRect: dragOverlay.rect,\n      scrollableAncestors,\n      scrollableAncestorRects,\n      transform,\n      windowRect,\n    });\n    const initialRect = useInitialValue(activeNodeRect);\n    const dropAnimation = useDropAnimation({\n      config: dropAnimationConfig,\n      draggableNodes,\n      droppableContainers,\n      measuringConfiguration,\n    });\n    // We need to wait for the active node to be measured before connecting the drag overlay ref\n    // otherwise collisions can be computed against a mispositioned drag overlay\n    const ref = initialRect ? dragOverlay.setRef : undefined;\n\n    return (\n      <NullifiedContextProvider>\n        <AnimationManager animation={dropAnimation}>\n          {active && key ? (\n            <PositionedOverlay\n              key={key}\n              id={active.id}\n              ref={ref}\n              as={wrapperElement}\n              activatorEvent={activatorEvent}\n              adjustScale={adjustScale}\n              className={className}\n              transition={transition}\n              rect={initialRect}\n              style={{\n                zIndex,\n                ...style,\n              }}\n              transform={modifiedTransform}\n            >\n              {children}\n            </PositionedOverlay>\n          ) : null}\n        </AnimationManager>\n      </NullifiedContextProvider>\n    );\n  }\n);\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAMA,eAAoC;EACxCC,SAAS;AAD+B;SAI1BC,WAAAA,MAAAA;MAAW;IAACC;IAAIC;;AAC9B,SACEC,aAAAA,QAAAA,cAAA,OAAA;IAAKF;IAAQG,OAAON;KACjBI,KADH;AAIH;SCTeG,WAAAA,MAAAA;MAAW;IAACJ;IAAIK;IAAcC,eAAe;;AAE3D,QAAMC,iBAAsC;IAC1CC,UAAU;IACVC,KAAK;IACLC,MAAM;IACNC,OAAO;IACPC,QAAQ;IACRC,QAAQ;IACRC,QAAQ;IACRC,SAAS;IACTC,UAAU;IACVC,MAAM;IACNC,UAAU;IACVC,YAAY;;AAGd,SACEjB,aAAAA,QAAAA,cAAA,OAAA;IACEF;IACAG,OAAOI;IACPa,MAAK;iBACMd;;KAGVD,YAPH;AAUH;SClCegB,kBAAAA;AACd,QAAM,CAAChB,cAAciB,eAAf,QAAkCC,uBAAS,EAAD;AAChD,QAAMC,eAAWC,0BAAaxB,WAAD;AAC3B,QAAIA,SAAS,MAAM;AACjBqB,sBAAgBrB,KAAD;;KAEhB,CAAA,CAJyB;AAM5B,SAAO;IAACuB;IAAUnB;;AACnB;;;ACPM,IAAMqB,wBAAoBC,6BAAuC,IAA1B;SCC9BC,cAAcC,UAAAA;AAC5B,QAAMC,uBAAmBC,0BAAWL,iBAAD;AAEnCM,+BAAU,MAAA;AACR,QAAI,CAACF,kBAAkB;AACrB,YAAM,IAAIG,MACR,8DADI;;AAKR,UAAMC,cAAcJ,iBAAiBD,QAAD;AAEpC,WAAOK;KACN,CAACL,UAAUC,gBAAX,CAVM;AAWV;SCfeK,wBAAAA;AACd,QAAM,CAACC,SAAD,QAAcC,wBAAS,MAAM,oBAAIC,IAAJ,CAAP;AAE5B,QAAMR,uBAAmBS,2BACtBV,cAAD;AACEO,cAAUI,IAAIX,QAAd;AACA,WAAO,MAAMO,UAAUK,OAAOZ,QAAjB;KAEf,CAACO,SAAD,CALkC;AAQpC,QAAMM,eAAWH,2BACf,UAAA;QAAC;MAACI;MAAMC;;AACNR,cAAUS,QAAShB,cAAD;AAAA,UAAA;AAAA,cAAA,iBAAcA,SAASc,IAAD,MAAtB,OAAA,SAAc,eAAA,KAAAd,UAAiBe,KAAT;KAAxC;KAEF,CAACR,SAAD,CAJ0B;AAO5B,SAAO,CAACM,UAAUZ,gBAAX;AACR;ICrBYgB,kCAA4D;EACvEC,WAAS;AAD8D;AAQzE,IAAaC,uBAAsC;EACjDC,YAAW,MAAA;QAAC;MAACC;;AACX,WAAA,8BAAmCA,OAAOC,KAA1C;;EAEFC,WAAU,OAAA;QAAC;MAACF;MAAQG;;AAClB,QAAIA,MAAM;AACR,aAAA,oBAAyBH,OAAOC,KAAhC,oCAAoEE,KAAKF,KAAzE;;AAGF,WAAA,oBAAyBD,OAAOC,KAAhC;;EAEFG,UAAS,OAAA;QAAC;MAACJ;MAAQG;;AACjB,QAAIA,MAAM;AACR,aAAA,oBAAyBH,OAAOC,KAAhC,sCAAsEE,KAAKF;;AAG7E,WAAA,oBAAyBD,OAAOC,KAAhC;;EAEFI,aAAY,OAAA;QAAC;MAACL;;AACZ,WAAA,4CAAiDA,OAAOC,KAAxD;;AAnB+C;SCUnCK,cAAAA,MAAAA;MAAc;IAC5BC,gBAAgBT;IAChBU;IACAC;IACAC,2BAA2Bd;;AAE3B,QAAM;IAACe;IAAUC;MAAgBC,gBAAe;AAChD,QAAMC,eAAeC,YAAW,eAAA;AAChC,QAAM,CAACC,SAASC,UAAV,QAAwB9B,wBAAS,KAAD;AAEtCL,+BAAU,MAAA;AACRmC,eAAW,IAAD;KACT,CAAA,CAFM;AAITvC,oBACEwC,uBACE,OAAO;IACLnB,YAAW,OAAA;UAAC;QAACC;;AACXW,eAASJ,cAAcR,YAAY;QAACC;OAA3B,CAAD;;IAEVmB,WAAU,OAAA;UAAC;QAACnB;QAAQG;;AAClB,UAAII,cAAcY,YAAY;AAC5BR,iBAASJ,cAAcY,WAAW;UAACnB;UAAQG;SAAlC,CAAD;;;IAGZD,WAAU,OAAA;UAAC;QAACF;QAAQG;;AAClBQ,eAASJ,cAAcL,WAAW;QAACF;QAAQG;OAAlC,CAAD;;IAEVC,UAAS,OAAA;UAAC;QAACJ;QAAQG;;AACjBQ,eAASJ,cAAcH,UAAU;QAACJ;QAAQG;OAAjC,CAAD;;IAEVE,aAAY,OAAA;UAAC;QAACL;QAAQG;;AACpBQ,eAASJ,cAAcF,aAAa;QAACL;QAAQG;OAApC,CAAD;;MAGZ,CAACQ,UAAUJ,aAAX,CApBK,CADI;AAyBb,MAAI,CAACS,SAAS;AACZ,WAAO;;AAGT,QAAMI,SACJC,cAAAA,QAAAA,cAAA,cAAAA,QAAA,UAAA,MACEA,cAAAA,QAAAA,cAACC,YAAD;IACErB,IAAIQ;IACJc,OAAOb,yBAAyBb;GAFlC,GAIAwB,cAAAA,QAAAA,cAACG,YAAD;IAAYvB,IAAIa;IAAcF;GAA9B,CALF;AASF,SAAOJ,gBAAYiB,+BAAaL,QAAQZ,SAAT,IAAsBY;AACtD;ACvED,IAAYM;CAAZ,SAAYA,SAAAA;AACVA,EAAAA,QAAAA,WAAAA,IAAA;AACAA,EAAAA,QAAAA,UAAAA,IAAA;AACAA,EAAAA,QAAAA,SAAAA,IAAA;AACAA,EAAAA,QAAAA,YAAAA,IAAA;AACAA,EAAAA,QAAAA,UAAAA,IAAA;AACAA,EAAAA,QAAAA,mBAAAA,IAAA;AACAA,EAAAA,QAAAA,sBAAAA,IAAA;AACAA,EAAAA,QAAAA,qBAAAA,IAAA;AACD,GATWA,WAAAA,SAAM,CAAA,EAAlB;SCHgBC,OAAAA;AAAAA;SCIAC,UACdC,QACAC,SAAAA;AAEA,aAAOZ;IACL,OAAO;MACLW;MACAC,SAASA,WAAF,OAAEA,UAAY,CAAA;;;IAGvB,CAACD,QAAQC,OAAT;EANY;AAQf;SCZeC,aAAAA;oCACXC,UAAAA,IAAAA,MAAAA,IAAAA,GAAAA,OAAAA,GAAAA,OAAAA,MAAAA,QAAAA;AAAAA,YAAAA,IAAAA,IAAAA,UAAAA,IAAAA;;AAEH,aAAOd;IACL,MACE,CAAC,GAAGc,OAAJ,EAAaC,OACVJ,YAA4CA,UAAU,IADzD;;IAIF,CAAC,GAAGG,OAAJ;EANY;AAQf;ICbYE,qBAAkCC,OAAOC,OAAO;EAC3DC,GAAG;EACHC,GAAG;AAFwD,CAAd;ACG/C,SAAgBC,gBAAgBC,IAAiBC,IAAAA;AAC/C,SAAOC,KAAKC,KAAKD,KAAKE,IAAIJ,GAAGH,IAAII,GAAGJ,GAAG,CAAtB,IAA2BK,KAAKE,IAAIJ,GAAGF,IAAIG,GAAGH,GAAG,CAAtB,CAArC;AACR;SCJeO,2BACdnD,OACAoD,MAAAA;AAEA,QAAMC,mBAAmBC,oBAAoBtD,KAAD;AAE5C,MAAI,CAACqD,kBAAkB;AACrB,WAAO;;AAGT,QAAME,kBAAkB;IACtBZ,IAAKU,iBAAiBV,IAAIS,KAAKI,QAAQJ,KAAKK,QAAS;IACrDb,IAAKS,iBAAiBT,IAAIQ,KAAKM,OAAON,KAAKO,SAAU;;AAGvD,SAAUJ,gBAAgBZ,IAA1B,OAAgCY,gBAAgBX,IAAhD;AACD;ACXD,SAAgBgB,kBAAAA,MAAAA,OAAAA;MACd;IAACC,MAAM;MAAChC,OAAOiC;;;MACf;IAACD,MAAM;MAAChC,OAAOkC;;;AAEf,SAAOD,IAAIC;AACZ;AAKD,SAAgBC,mBAAAA,OAAAA,OAAAA;MACd;IAACH,MAAM;MAAChC,OAAOiC;;;MACf;IAACD,MAAM;MAAChC,OAAOkC;;;AAEf,SAAOA,IAAID;AACZ;AAMD,SAAgBG,mBAAAA,OAAAA;MAAmB;IAACT;IAAME;IAAKC;IAAQF;;AACrD,SAAO,CACL;IACEd,GAAGa;IACHZ,GAAGc;KAEL;IACEf,GAAGa,OAAOC;IACVb,GAAGc;KAEL;IACEf,GAAGa;IACHZ,GAAGc,MAAMC;KAEX;IACEhB,GAAGa,OAAOC;IACVb,GAAGc,MAAMC;GAfN;AAkBR;AAaD,SAAgBO,kBACdC,YACAC,UAAAA;AAEA,MAAI,CAACD,cAAcA,WAAWE,WAAW,GAAG;AAC1C,WAAO;;AAGT,QAAM,CAACC,cAAD,IAAmBH;AAEzB,SAAOC,WAAWE,eAAeF,QAAD,IAAaE;AAC9C;AC/DD,SAASC,kBACPnB,MACAI,MACAE,KAHF;MAEEF,SAAAA,QAAAA;AAAAA,WAAOJ,KAAKI;;MACZE,QAAAA,QAAAA;AAAAA,UAAMN,KAAKM;;AAEX,SAAO;IACLf,GAAGa,OAAOJ,KAAKK,QAAQ;IACvBb,GAAGc,MAAMN,KAAKO,SAAS;;AAE1B;AAMD,IAAaa,gBAAoC,UAAA;MAAC;IAChDC;IACAC;IACAC;;AAEA,QAAMC,aAAaL,kBACjBE,eACAA,cAAcjB,MACdiB,cAAcf,GAHoB;AAKpC,QAAMS,aAAoC,CAAA;AAE1C,aAAWU,sBAAsBF,qBAAqB;AACpD,UAAM;MAACpE;QAAMsE;AACb,UAAMzB,OAAOsB,eAAeI,IAAIvE,EAAnB;AAEb,QAAI6C,MAAM;AACR,YAAM2B,cAAclC,gBAAgB0B,kBAAkBnB,IAAD,GAAQwB,UAA1B;AAEnCT,iBAAWa,KAAK;QAACzE;QAAIsD,MAAM;UAACgB;UAAoBhD,OAAOkD;;OAAvD;;;AAIJ,SAAOZ,WAAWc,KAAKrB,iBAAhB;AACR;ACvCD,IAAasB,iBAAqC,UAAA;MAAC;IACjDT;IACAC;IACAC;;AAEA,QAAMQ,UAAUlB,mBAAmBQ,aAAD;AAClC,QAAMN,aAAoC,CAAA;AAE1C,aAAWU,sBAAsBF,qBAAqB;AACpD,UAAM;MAACpE;QAAMsE;AACb,UAAMzB,OAAOsB,eAAeI,IAAIvE,EAAnB;AAEb,QAAI6C,MAAM;AACR,YAAMgC,cAAcnB,mBAAmBb,IAAD;AACtC,YAAMiC,YAAYF,QAAQG,OAAO,CAACC,aAAaC,QAAQC,UAAtB;AAC/B,eAAOF,cAAc1C,gBAAgBuC,YAAYK,KAAD,GAASD,MAArB;SACnC,CAFe;AAGlB,YAAME,oBAAoBC,QAAQN,YAAY,GAAGO,QAAQ,CAAxB,CAAD;AAEhCzB,iBAAWa,KAAK;QACdzE;QACAsD,MAAM;UAACgB;UAAoBhD,OAAO6D;;OAFpC;;;AAOJ,SAAOvB,WAAWc,KAAKrB,iBAAhB;AACR;AC5BD,SAAgBiC,qBACdC,OACAC,QAAAA;AAEA,QAAMrC,MAAMV,KAAKgD,IAAID,OAAOrC,KAAKoC,MAAMpC,GAA3B;AACZ,QAAMF,OAAOR,KAAKgD,IAAID,OAAOvC,MAAMsC,MAAMtC,IAA5B;AACb,QAAMyC,QAAQjD,KAAKkD,IAAIH,OAAOvC,OAAOuC,OAAOtC,OAAOqC,MAAMtC,OAAOsC,MAAMrC,KAAxD;AACd,QAAM0C,SAASnD,KAAKkD,IAAIH,OAAOrC,MAAMqC,OAAOpC,QAAQmC,MAAMpC,MAAMoC,MAAMnC,MAAvD;AACf,QAAMF,QAAQwC,QAAQzC;AACtB,QAAMG,SAASwC,SAASzC;AAExB,MAAIF,OAAOyC,SAASvC,MAAMyC,QAAQ;AAChC,UAAMC,aAAaL,OAAOtC,QAAQsC,OAAOpC;AACzC,UAAM0C,YAAYP,MAAMrC,QAAQqC,MAAMnC;AACtC,UAAM2C,mBAAmB7C,QAAQE;AACjC,UAAM4C,oBACJD,oBAAoBF,aAAaC,YAAYC;AAE/C,WAAOX,OAAOY,kBAAkBX,QAAQ,CAA1B,CAAD;;AAIf,SAAO;AACR;AAMD,IAAaY,mBAAuC,UAAA;MAAC;IACnD/B;IACAC;IACAC;;AAEA,QAAMR,aAAoC,CAAA;AAE1C,aAAWU,sBAAsBF,qBAAqB;AACpD,UAAM;MAACpE;QAAMsE;AACb,UAAMzB,OAAOsB,eAAeI,IAAIvE,EAAnB;AAEb,QAAI6C,MAAM;AACR,YAAMmD,oBAAoBV,qBAAqBzC,MAAMqB,aAAP;AAE9C,UAAI8B,oBAAoB,GAAG;AACzBpC,mBAAWa,KAAK;UACdzE;UACAsD,MAAM;YAACgB;YAAoBhD,OAAO0E;;SAFpC;;;;AAQN,SAAOpC,WAAWc,KAAKjB,kBAAhB;AACR;ACpDD,SAASyC,kBAAkBC,OAAoBtD,MAA/C;AACE,QAAM;IAACM;IAAKF;IAAM2C;IAAQF;MAAS7C;AAEnC,SACEM,OAAOgD,MAAM9D,KAAK8D,MAAM9D,KAAKuD,UAAU3C,QAAQkD,MAAM/D,KAAK+D,MAAM/D,KAAKsD;AAExE;AAKD,IAAaU,gBAAoC,UAAA;MAAC;IAChDhC;IACAD;IACAkC;;AAEA,MAAI,CAACA,oBAAoB;AACvB,WAAO,CAAA;;AAGT,QAAMzC,aAAoC,CAAA;AAE1C,aAAWU,sBAAsBF,qBAAqB;AACpD,UAAM;MAACpE;QAAMsE;AACb,UAAMzB,OAAOsB,eAAeI,IAAIvE,EAAnB;AAEb,QAAI6C,QAAQqD,kBAAkBG,oBAAoBxD,IAArB,GAA4B;AAMvD,YAAM+B,UAAUlB,mBAAmBb,IAAD;AAClC,YAAMiC,YAAYF,QAAQG,OAAO,CAACC,aAAaC,WAAd;AAC/B,eAAOD,cAAc1C,gBAAgB+D,oBAAoBpB,MAArB;SACnC,CAFe;AAGlB,YAAME,oBAAoBC,QAAQN,YAAY,GAAGO,QAAQ,CAAxB,CAAD;AAEhCzB,iBAAWa,KAAK;QACdzE;QACAsD,MAAM;UAACgB;UAAoBhD,OAAO6D;;OAFpC;;;AAOJ,SAAOvB,WAAWc,KAAKrB,iBAAhB;AACR;SCpDeiD,YACdC,WACAC,OACAC,OAAAA;AAEA,SAAO;IACL,GAAGF;IACHG,QAAQF,SAASC,QAAQD,MAAMtD,QAAQuD,MAAMvD,QAAQ;IACrDyD,QAAQH,SAASC,QAAQD,MAAMpD,SAASqD,MAAMrD,SAAS;;AAE1D;SCVewD,aACdJ,OACAC,OAAAA;AAEA,SAAOD,SAASC,QACZ;IACErE,GAAGoE,MAAMvD,OAAOwD,MAAMxD;IACtBZ,GAAGmE,MAAMrD,MAAMsD,MAAMtD;MAEvBlB;AACL;SCXe4E,uBAAuBC,UAAAA;AACrC,SAAO,SAASC,iBACdlE,MADK;sCAEFmE,cAAAA,IAAAA,MAAAA,OAAAA,IAAAA,OAAAA,IAAAA,CAAAA,GAAAA,OAAAA,GAAAA,OAAAA,MAAAA,QAAAA;AAAAA,kBAAAA,OAAAA,CAAAA,IAAAA,UAAAA,IAAAA;;AAEH,WAAOA,YAAYjC,OACjB,CAACkC,KAAKC,gBAAgB;MACpB,GAAGD;MACH9D,KAAK8D,IAAI9D,MAAM2D,WAAWI,WAAW7E;MACrCuD,QAAQqB,IAAIrB,SAASkB,WAAWI,WAAW7E;MAC3CY,MAAMgE,IAAIhE,OAAO6D,WAAWI,WAAW9E;MACvCsD,OAAOuB,IAAIvB,QAAQoB,WAAWI,WAAW9E;QAE3C;MAAC,GAAGS;KARC;;AAWV;AAEM,IAAMsE,kBAAkBN,uBAAuB,CAAD;SClBrCO,eAAeb,WAAAA;AAC7B,MAAIA,UAAUc,WAAW,WAArB,GAAmC;AACrC,UAAMC,iBAAiBf,UAAUgB,MAAM,GAAG,EAAnB,EAAuBC,MAAM,IAA7B;AAEvB,WAAO;MACLpF,GAAG,CAACkF,eAAe,EAAD;MAClBjF,GAAG,CAACiF,eAAe,EAAD;MAClBZ,QAAQ,CAACY,eAAe,CAAD;MACvBX,QAAQ,CAACW,eAAe,CAAD;;aAEhBf,UAAUc,WAAW,SAArB,GAAiC;AAC1C,UAAMC,iBAAiBf,UAAUgB,MAAM,GAAG,EAAnB,EAAuBC,MAAM,IAA7B;AAEvB,WAAO;MACLpF,GAAG,CAACkF,eAAe,CAAD;MAClBjF,GAAG,CAACiF,eAAe,CAAD;MAClBZ,QAAQ,CAACY,eAAe,CAAD;MACvBX,QAAQ,CAACW,eAAe,CAAD;;;AAI3B,SAAO;AACR;SCpBeG,iBACd5E,MACA0D,WACAvD,iBAAAA;AAEA,QAAM0E,kBAAkBN,eAAeb,SAAD;AAEtC,MAAI,CAACmB,iBAAiB;AACpB,WAAO7E;;AAGT,QAAM;IAAC6D;IAAQC;IAAQvE,GAAGuF;IAAYtF,GAAGuF;MAAcF;AAEvD,QAAMtF,IAAIS,KAAKI,OAAO0E,cAAc,IAAIjB,UAAUmB,WAAW7E,eAAD;AAC5D,QAAMX,IACJQ,KAAKM,MACLyE,cACC,IAAIjB,UACHkB,WAAW7E,gBAAgBuE,MAAMvE,gBAAgB8E,QAAQ,GAAxB,IAA+B,CAArD,CAAD;AACd,QAAMC,IAAIrB,SAAS7D,KAAKK,QAAQwD,SAAS7D,KAAKK;AAC9C,QAAM8E,IAAIrB,SAAS9D,KAAKO,SAASuD,SAAS9D,KAAKO;AAE/C,SAAO;IACLF,OAAO6E;IACP3E,QAAQ4E;IACR7E,KAAKd;IACLqD,OAAOtD,IAAI2F;IACXnC,QAAQvD,IAAI2F;IACZ/E,MAAMb;;AAET;ACzBD,IAAM6F,iBAA0B;EAACC,iBAAiB;AAAlB;AAKhC,SAAgBC,cACdC,SACAvG,SAAAA;MAAAA,YAAAA,QAAAA;AAAAA,cAAmBoG;;AAEnB,MAAIpF,OAAmBuF,QAAQC,sBAAR;AAEvB,MAAIxG,QAAQqG,iBAAiB;AAC3B,UAAM;MAAC3B;MAAWvD;QAChBsF,UAAUF,OAAD,EAAUG,iBAAiBH,OAApC;AAEF,QAAI7B,WAAW;AACb1D,aAAO4E,iBAAiB5E,MAAM0D,WAAWvD,eAAlB;;;AAI3B,QAAM;IAACG;IAAKF;IAAMC;IAAOE;IAAQwC;IAAQF;MAAS7C;AAElD,SAAO;IACLM;IACAF;IACAC;IACAE;IACAwC;IACAF;;AAEH;AAUD,SAAgB8C,+BAA+BJ,SAAAA;AAC7C,SAAOD,cAAcC,SAAS;IAACF,iBAAiB;GAA5B;AACrB;SCjDeO,oBAAoBL,SAAAA;AAClC,QAAMlF,QAAQkF,QAAQM;AACtB,QAAMtF,SAASgF,QAAQO;AAEvB,SAAO;IACLxF,KAAK;IACLF,MAAM;IACNyC,OAAOxC;IACP0C,QAAQxC;IACRF;IACAE;;AAEH;SCZewF,QACdC,MACAC,eAAAA;MAAAA,kBAAAA,QAAAA;AAAAA,oBAAqCR,UAAUO,IAAD,EAAON,iBAAiBM,IAAjC;;AAErC,SAAOC,cAAcC,aAAa;AACnC;SCLeC,aACdZ,SACAU,eAAAA;MAAAA,kBAAAA,QAAAA;AAAAA,oBAAqCR,UAAUF,OAAD,EAAUG,iBACtDH,OADmC;;AAIrC,QAAMa,gBAAgB;AACtB,QAAMC,cAAa,CAAC,YAAY,aAAa,WAA1B;AAEnB,SAAOA,YAAWC,KAAMtF,cAAD;AACrB,UAAMvC,QAAQwH,cAAcjF,QAAD;AAE3B,WAAO,OAAOvC,UAAU,WAAW2H,cAAcG,KAAK9H,KAAnB,IAA4B;GAH1D;AAKR;SCNe+H,uBACdjB,SACAkB,OAAAA;AAEA,QAAMC,gBAA2B,CAAA;AAEjC,WAASC,wBAAwBX,MAAjC;AACE,QAAIS,SAAS,QAAQC,cAAczF,UAAUwF,OAAO;AAClD,aAAOC;;AAGT,QAAI,CAACV,MAAM;AACT,aAAOU;;AAGT,QACEE,WAAWZ,IAAD,KACVA,KAAKa,oBAAoB,QACzB,CAACH,cAAcI,SAASd,KAAKa,gBAA5B,GACD;AACAH,oBAAc9E,KAAKoE,KAAKa,gBAAxB;AAEA,aAAOH;;AAGT,QAAI,CAACK,cAAcf,IAAD,KAAUgB,aAAahB,IAAD,GAAQ;AAC9C,aAAOU;;AAGT,QAAIA,cAAcI,SAASd,IAAvB,GAA8B;AAChC,aAAOU;;AAGT,UAAMT,gBAAgBR,UAAUF,OAAD,EAAUG,iBAAiBM,IAApC;AAEtB,QAAIA,SAAST,SAAS;AACpB,UAAIY,aAAaH,MAAMC,aAAP,GAAuB;AACrCS,sBAAc9E,KAAKoE,IAAnB;;;AAIJ,QAAID,QAAQC,MAAMC,aAAP,GAAuB;AAChC,aAAOS;;AAGT,WAAOC,wBAAwBX,KAAKiB,UAAN;;AAGhC,MAAI,CAAC1B,SAAS;AACZ,WAAOmB;;AAGT,SAAOC,wBAAwBpB,OAAD;AAC/B;AAED,SAAgB2B,2BAA2BlB,MAAAA;AACzC,QAAM,CAACmB,uBAAD,IAA4BX,uBAAuBR,MAAM,CAAP;AAExD,SAAOmB,2BAAP,OAAOA,0BAA2B;AACnC;SC5DeC,qBAAqB7B,SAAAA;AACnC,MAAI,CAAC8B,aAAa,CAAC9B,SAAS;AAC1B,WAAO;;AAGT,MAAI+B,SAAS/B,OAAD,GAAW;AACrB,WAAOA;;AAGT,MAAI,CAACgC,OAAOhC,OAAD,GAAW;AACpB,WAAO;;AAGT,MACEqB,WAAWrB,OAAD,KACVA,YAAYiC,iBAAiBjC,OAAD,EAAUsB,kBACtC;AACA,WAAOY;;AAGT,MAAIV,cAAcxB,OAAD,GAAW;AAC1B,WAAOA;;AAGT,SAAO;AACR;SC9BemC,qBAAqBnC,SAAAA;AACnC,MAAI+B,SAAS/B,OAAD,GAAW;AACrB,WAAOA,QAAQoC;;AAGjB,SAAOpC,QAAQqC;AAChB;AAED,SAAgBC,qBAAqBtC,SAAAA;AACnC,MAAI+B,SAAS/B,OAAD,GAAW;AACrB,WAAOA,QAAQuC;;AAGjB,SAAOvC,QAAQwC;AAChB;AAED,SAAgBC,qBACdzC,SAAAA;AAEA,SAAO;IACLhG,GAAGmI,qBAAqBnC,OAAD;IACvB/F,GAAGqI,qBAAqBtC,OAAD;;AAE1B;AC3BD,IAAY0C;CAAZ,SAAYA,YAAAA;AACVA,EAAAA,WAAAA,WAAAA,SAAAA,IAAAA,CAAAA,IAAA;AACAA,EAAAA,WAAAA,WAAAA,UAAAA,IAAAA,EAAAA,IAAA;AACD,GAHWA,cAAAA,YAAS,CAAA,EAArB;SCEgBC,2BAA2B3C,SAAAA;AACzC,MAAI,CAAC8B,aAAa,CAAC9B,SAAS;AAC1B,WAAO;;AAGT,SAAOA,YAAY4C,SAAStB;AAC7B;SCNeuB,kBAAkBC,oBAAAA;AAChC,QAAMC,YAAY;IAChB/I,GAAG;IACHC,GAAG;;AAEL,QAAM+I,aAAaL,2BAA2BG,kBAAD,IACzC;IACE9H,QAAQkH,OAAO3B;IACfzF,OAAOoH,OAAO5B;MAEhB;IACEtF,QAAQ8H,mBAAmBG;IAC3BnI,OAAOgI,mBAAmBI;;AAEhC,QAAMC,YAAY;IAChBnJ,GAAG8I,mBAAmBM,cAAcJ,WAAWlI;IAC/Cb,GAAG6I,mBAAmBO,eAAeL,WAAWhI;;AAGlD,QAAMsI,QAAQR,mBAAmBN,aAAaO,UAAU9I;AACxD,QAAMsJ,SAAST,mBAAmBT,cAAcU,UAAU/I;AAC1D,QAAMwJ,WAAWV,mBAAmBN,aAAaW,UAAUlJ;AAC3D,QAAMwJ,UAAUX,mBAAmBT,cAAcc,UAAUnJ;AAE3D,SAAO;IACLsJ;IACAC;IACAC;IACAC;IACAN;IACAJ;;AAEH;AC5BD,IAAMW,mBAAmB;EACvB1J,GAAG;EACHC,GAAG;AAFoB;AAKzB,SAAgB0J,2BACdC,iBACAC,qBAAAA,MAEAC,cACAC,qBAAAA;MAFA;IAAChJ;IAAKF;IAAMyC;IAAOE;;MACnBsG,iBAAAA,QAAAA;AAAAA,mBAAe;;MACfC,wBAAAA,QAAAA;AAAAA,0BAAsBL;;AAEtB,QAAM;IAACJ;IAAOE;IAAUD;IAAQE;MAAWZ,kBAAkBe,eAAD;AAE5D,QAAMI,YAAY;IAChBhK,GAAG;IACHC,GAAG;;AAEL,QAAMgK,QAAQ;IACZjK,GAAG;IACHC,GAAG;;AAEL,QAAMiK,YAAY;IAChBlJ,QAAQ6I,oBAAoB7I,SAAS+I,oBAAoB9J;IACzDa,OAAO+I,oBAAoB/I,QAAQiJ,oBAAoB/J;;AAGzD,MAAI,CAACsJ,SAASvI,OAAO8I,oBAAoB9I,MAAMmJ,UAAUlJ,QAAQ;AAE/DgJ,cAAU/J,IAAIyI,UAAUyB;AACxBF,UAAMhK,IACJ6J,eACAzJ,KAAK+J,KACFP,oBAAoB9I,MAAMmJ,UAAUlJ,SAASD,OAAOmJ,UAAUlJ,MADjE;aAIF,CAACwI,YACDhG,UAAUqG,oBAAoBrG,SAAS0G,UAAUlJ,QACjD;AAEAgJ,cAAU/J,IAAIyI,UAAU2B;AACxBJ,UAAMhK,IACJ6J,eACAzJ,KAAK+J,KACFP,oBAAoBrG,SAAS0G,UAAUlJ,SAASwC,UAC/C0G,UAAUlJ,MAFd;;AAMJ,MAAI,CAACyI,WAAWnG,SAASuG,oBAAoBvG,QAAQ4G,UAAUpJ,OAAO;AAEpEkJ,cAAUhK,IAAI0I,UAAU2B;AACxBJ,UAAMjK,IACJ8J,eACAzJ,KAAK+J,KACFP,oBAAoBvG,QAAQ4G,UAAUpJ,QAAQwC,SAAS4G,UAAUpJ,KADpE;aAGO,CAACyI,UAAU1I,QAAQgJ,oBAAoBhJ,OAAOqJ,UAAUpJ,OAAO;AAExEkJ,cAAUhK,IAAI0I,UAAUyB;AACxBF,UAAMjK,IACJ8J,eACAzJ,KAAK+J,KACFP,oBAAoBhJ,OAAOqJ,UAAUpJ,QAAQD,QAAQqJ,UAAUpJ,KADlE;;AAKJ,SAAO;IACLkJ;IACAC;;AAEH;SC7EeK,qBAAqBtE,SAAAA;AACnC,MAAIA,YAAY4C,SAAStB,kBAAkB;AACzC,UAAM;MAAChB;MAAYC;QAAe2B;AAElC,WAAO;MACLnH,KAAK;MACLF,MAAM;MACNyC,OAAOgD;MACP9C,QAAQ+C;MACRzF,OAAOwF;MACPtF,QAAQuF;;;AAIZ,QAAM;IAACxF;IAAKF;IAAMyC;IAAOE;MAAUwC,QAAQC,sBAAR;AAEnC,SAAO;IACLlF;IACAF;IACAyC;IACAE;IACA1C,OAAOkF,QAAQkD;IACflI,QAAQgF,QAAQiD;;AAEnB;SCdesB,iBAAiBC,qBAAAA;AAC/B,SAAOA,oBAAoB7H,OAAoB,CAACkC,KAAK4B,SAAN;AAC7C,WAAOxJ,IAAI4H,KAAK4D,qBAAqBhC,IAAD,CAA1B;KACT5G,kBAFI;AAGR;AAED,SAAgB4K,iBAAiBD,qBAAAA;AAC/B,SAAOA,oBAAoB7H,OAAe,CAACkC,KAAK4B,SAAN;AACxC,WAAO5B,MAAMsD,qBAAqB1B,IAAD;KAChC,CAFI;AAGR;AAED,SAAgBiE,iBAAiBF,qBAAAA;AAC/B,SAAOA,oBAAoB7H,OAAe,CAACkC,KAAK4B,SAAN;AACxC,WAAO5B,MAAMyD,qBAAqB7B,IAAD;KAChC,CAFI;AAGR;SCtBekE,uBACd3E,SACA4E,SAAAA;MAAAA,YAAAA,QAAAA;AAAAA,cAA6C7E;;AAE7C,MAAI,CAACC,SAAS;AACZ;;AAGF,QAAM;IAACjF;IAAKF;IAAM2C;IAAQF;MAASsH,QAAQ5E,OAAD;AAC1C,QAAM4B,0BAA0BD,2BAA2B3B,OAAD;AAE1D,MAAI,CAAC4B,yBAAyB;AAC5B;;AAGF,MACEpE,UAAU,KACVF,SAAS,KACTvC,OAAOmH,OAAO3B,eACd1F,QAAQqH,OAAO5B,YACf;AACAN,YAAQ6E,eAAe;MACrBC,OAAO;MACPC,QAAQ;KAFV;;AAKH;ACtBD,IAAMjE,aAAa,CACjB,CAAC,KAAK,CAAC,QAAQ,OAAT,GAAmB2D,gBAAzB,GACA,CAAC,KAAK,CAAC,OAAO,QAAR,GAAmBC,gBAAzB,CAFiB;AAKnB,IAAaM,OAAb,MAAaA;EACXC,YAAYxK,MAAkBuF,SAAAA;SAyBtBvF,OAAAA;SAEDK,QAAAA;SAEAE,SAAAA;SAIAD,MAAAA;SAEAyC,SAAAA;SAEAF,QAAAA;SAEAzC,OAAAA;AAtCL,UAAM2J,sBAAsBvD,uBAAuBjB,OAAD;AAClD,UAAMkF,gBAAgBX,iBAAiBC,mBAAD;AAEtC,SAAK/J,OAAO;MAAC,GAAGA;;AAChB,SAAKK,QAAQL,KAAKK;AAClB,SAAKE,SAASP,KAAKO;AAEnB,eAAW,CAACmK,MAAMC,MAAMC,eAAb,KAAiCvE,YAAY;AACtD,iBAAWwE,QAAOF,MAAM;AACtBtL,eAAOyL,eAAe,MAAMD,MAAK;UAC/BnJ,KAAK,MAAA;AACH,kBAAMqJ,iBAAiBH,gBAAgBb,mBAAD;AACtC,kBAAMiB,sBAAsBP,cAAcC,IAAD,IAASK;AAElD,mBAAO,KAAK/K,KAAK6K,IAAV,IAAiBG;;UAE1BC,YAAY;SAPd;;;AAYJ5L,WAAOyL,eAAe,MAAM,QAAQ;MAACG,YAAY;KAAjD;;;ICpCSC,kBAAAA;EAOXV,YAAoB7H,QAAAA;SAAAA,SAAAA;SANZvG,YAIF,CAAA;SAaC+O,YAAY,MAAA;AACjB,WAAK/O,UAAUS,QAAShB,cAAD;AAAA,YAAA;AAAA,gBAAA,eACrB,KAAK8G,WADgB,OAAA,SACrB,aAAayI,oBAAoB,GAAGvP,QAApC;OADF;;AAZkB,SAAA,SAAA8G;;EAEbnG,IACL6O,WACAC,SACAtM,SAHQ;;AAKR,KAAA,gBAAA,KAAK2D,WAAL,OAAA,SAAA,cAAa4I,iBAAiBF,WAAWC,SAA0BtM,OAAnE;AACA,SAAK5C,UAAUwF,KAAK,CAACyJ,WAAWC,SAA0BtM,OAAtC,CAApB;;;SCbYwM,uBACd7I,QAAAA;AAQA,QAAM;IAAC8I;MAAehG,UAAU9C,MAAD;AAE/B,SAAOA,kBAAkB8I,cAAc9I,SAAS6E,iBAAiB7E,MAAD;AACjE;SCZe+I,oBACdC,OACAC,aAAAA;AAEA,QAAMC,KAAKjM,KAAK+J,IAAIgC,MAAMpM,CAAf;AACX,QAAMuM,KAAKlM,KAAK+J,IAAIgC,MAAMnM,CAAf;AAEX,MAAI,OAAOoM,gBAAgB,UAAU;AACnC,WAAOhM,KAAKC,KAAKgM,MAAM,IAAIC,MAAM,CAA1B,IAA+BF;;AAGxC,MAAI,OAAOA,eAAe,OAAOA,aAAa;AAC5C,WAAOC,KAAKD,YAAYrM,KAAKuM,KAAKF,YAAYpM;;AAGhD,MAAI,OAAOoM,aAAa;AACtB,WAAOC,KAAKD,YAAYrM;;AAG1B,MAAI,OAAOqM,aAAa;AACtB,WAAOE,KAAKF,YAAYpM;;AAG1B,SAAO;AACR;AC1BD,IAAYuM;CAAZ,SAAYA,YAAAA;AACVA,EAAAA,WAAAA,OAAAA,IAAA;AACAA,EAAAA,WAAAA,WAAAA,IAAA;AACAA,EAAAA,WAAAA,SAAAA,IAAA;AACAA,EAAAA,WAAAA,aAAAA,IAAA;AACAA,EAAAA,WAAAA,QAAAA,IAAA;AACAA,EAAAA,WAAAA,iBAAAA,IAAA;AACAA,EAAAA,WAAAA,kBAAAA,IAAA;AACD,GARWA,cAAAA,YAAS,CAAA,EAArB;AAUA,SAAgBC,eAAepP,OAAAA;AAC7BA,QAAMoP,eAAN;AACD;AAED,SAAgBC,gBAAgBrP,OAAAA;AAC9BA,QAAMqP,gBAAN;AACD;ICbWC;CAAZ,SAAYA,eAAAA;AACVA,EAAAA,cAAAA,OAAAA,IAAA;AACAA,EAAAA,cAAAA,MAAAA,IAAA;AACAA,EAAAA,cAAAA,OAAAA,IAAA;AACAA,EAAAA,cAAAA,MAAAA,IAAA;AACAA,EAAAA,cAAAA,IAAAA,IAAA;AACAA,EAAAA,cAAAA,KAAAA,IAAA;AACAA,EAAAA,cAAAA,OAAAA,IAAA;AACAA,EAAAA,cAAAA,KAAAA,IAAA;AACD,GATWA,iBAAAA,eAAY,CAAA,EAAxB;ACDO,IAAMC,uBAAsC;EACjDC,OAAO,CAACF,aAAaG,OAAOH,aAAaI,KAAlC;EACPC,QAAQ,CAACL,aAAaM,GAAd;EACRC,KAAK,CAACP,aAAaG,OAAOH,aAAaI,OAAOJ,aAAaQ,GAAtD;AAH4C;AAMnD,IAAaC,kCAA4D,CACvE/P,OADuE,SAAA;MAEvE;IAACgQ;;AAED,UAAQhQ,MAAMiQ,MAAd;IACE,KAAKX,aAAaY;AAChB,aAAO;QACL,GAAGF;QACHrN,GAAGqN,mBAAmBrN,IAAI;;IAE9B,KAAK2M,aAAaa;AAChB,aAAO;QACL,GAAGH;QACHrN,GAAGqN,mBAAmBrN,IAAI;;IAE9B,KAAK2M,aAAac;AAChB,aAAO;QACL,GAAGJ;QACHpN,GAAGoN,mBAAmBpN,IAAI;;IAE9B,KAAK0M,aAAae;AAChB,aAAO;QACL,GAAGL;QACHpN,GAAGoN,mBAAmBpN,IAAI;;;AAIhC,SAAO0N;AACR;ICGYC,uBAAAA;EAMX3C,YAAoB4C,OAAAA;SAAAA,QAAAA;SALbC,oBAAoB;SACnBC,uBAAAA;SACAlR,YAAAA;SACAmR,kBAAAA;AAEY,SAAA,QAAAH;AAClB,UAAM;MACJxQ,OAAO;QAAC+F;;QACNyK;AAEJ,SAAKA,QAAQA;AACb,SAAKhR,YAAY,IAAI8O,UAAU1D,iBAAiB7E,MAAD,CAA9B;AACjB,SAAK4K,kBAAkB,IAAIrC,UAAUzF,UAAU9C,MAAD,CAAvB;AACvB,SAAK6K,gBAAgB,KAAKA,cAAcC,KAAK,IAAxB;AACrB,SAAKC,eAAe,KAAKA,aAAaD,KAAK,IAAvB;AAEpB,SAAKE,OAAL;;EAGMA,SAAM;AACZ,SAAKC,YAAL;AAEA,SAAKL,gBAAgB/Q,IAAIuP,UAAU8B,QAAQ,KAAKH,YAAhD;AACA,SAAKH,gBAAgB/Q,IAAIuP,UAAU+B,kBAAkB,KAAKJ,YAA1D;AAEAK,eAAW,MAAM,KAAK3R,UAAUI,IAAIuP,UAAUiC,SAAS,KAAKR,aAA3C,CAAP;;EAGJI,cAAW;AACjB,UAAM;MAACK;MAAYC;QAAW,KAAKd;AACnC,UAAMpH,OAAOiI,WAAWjI,KAAKmI;AAE7B,QAAInI,MAAM;AACRkE,6BAAuBlE,IAAD;;AAGxBkI,YAAQ9O,kBAAD;;EAGDoO,cAAc5Q,OAAD;AACnB,QAAIwR,gBAAgBxR,KAAD,GAAS;AAC1B,YAAM;QAACM;QAAQmR;QAASrP;UAAW,KAAKoO;AACxC,YAAM;QACJkB,gBAAgBnC;QAChBoC,mBAAmB5B;QACnB6B,iBAAiB;UACfxP;AACJ,YAAM;QAAC6N;UAAQjQ;AAEf,UAAI0R,cAAc7B,IAAI3F,SAAS+F,IAA3B,GAAkC;AACpC,aAAK4B,UAAU7R,KAAf;AACA;;AAGF,UAAI0R,cAAc/B,OAAOzF,SAAS+F,IAA9B,GAAqC;AACvC,aAAKa,aAAa9Q,KAAlB;AACA;;AAGF,YAAM;QAACyE;UAAiBgN,QAAQF;AAChC,YAAMvB,qBAAqBvL,gBACvB;QAAC9B,GAAG8B,cAAcjB;QAAMZ,GAAG6B,cAAcf;UACzClB;AAEJ,UAAI,CAAC,KAAKkO,sBAAsB;AAC9B,aAAKA,uBAAuBV;;AAG9B,YAAM8B,iBAAiBH,iBAAiB3R,OAAO;QAC7CM;QACAmR,SAASA,QAAQF;QACjBvB;OAHqC;AAMvC,UAAI8B,gBAAgB;AAClB,cAAMC,mBAAmBC,SACvBF,gBACA9B,kBAF0C;AAI5C,cAAMiC,cAAc;UAClBtP,GAAG;UACHC,GAAG;;AAEL,cAAM;UAACuK;YAAuBsE,QAAQF;AAEtC,mBAAWhF,mBAAmBY,qBAAqB;AACjD,gBAAMR,YAAY3M,MAAMiQ;AACxB,gBAAM;YAAChE;YAAOG;YAASF;YAAQC;YAAUL;YAAWJ;cAClDF,kBAAkBe,eAAD;AACnB,gBAAM2F,oBAAoBjF,qBAAqBV,eAAD;AAE9C,gBAAM4F,qBAAqB;YACzBxP,GAAGK,KAAKkD,IACNyG,cAAc2C,aAAaY,QACvBgC,kBAAkBjM,QAAQiM,kBAAkBzO,QAAQ,IACpDyO,kBAAkBjM,OACtBjD,KAAKgD,IACH2G,cAAc2C,aAAaY,QACvBgC,kBAAkB1O,OAClB0O,kBAAkB1O,OAAO0O,kBAAkBzO,QAAQ,GACvDqO,eAAenP,CAJjB,CAJC;YAWHC,GAAGI,KAAKkD,IACNyG,cAAc2C,aAAac,OACvB8B,kBAAkB/L,SAAS+L,kBAAkBvO,SAAS,IACtDuO,kBAAkB/L,QACtBnD,KAAKgD,IACH2G,cAAc2C,aAAac,OACvB8B,kBAAkBxO,MAClBwO,kBAAkBxO,MAAMwO,kBAAkBvO,SAAS,GACvDmO,eAAelP,CAJjB,CAJC;;AAaL,gBAAMwP,aACHzF,cAAc2C,aAAaY,SAAS,CAAC9D,WACrCO,cAAc2C,aAAaa,QAAQ,CAACjE;AACvC,gBAAMmG,aACH1F,cAAc2C,aAAac,QAAQ,CAACjE,YACpCQ,cAAc2C,aAAae,MAAM,CAACpE;AAErC,cAAImG,cAAcD,mBAAmBxP,MAAMmP,eAAenP,GAAG;AAC3D,kBAAM2P,uBACJ/F,gBAAgBvB,aAAa+G,iBAAiBpP;AAChD,kBAAM4P,4BACH5F,cAAc2C,aAAaY,SAC1BoC,wBAAwBxG,UAAUnJ,KACnCgK,cAAc2C,aAAaa,QAC1BmC,wBAAwB5G,UAAU/I;AAEtC,gBAAI4P,6BAA6B,CAACR,iBAAiBnP,GAAG;AAGpD2J,8BAAgBiG,SAAS;gBACvBhP,MAAM8O;gBACNG,UAAUb;eAFZ;AAIA;;AAGF,gBAAIW,2BAA2B;AAC7BN,0BAAYtP,IAAI4J,gBAAgBvB,aAAasH;mBACxC;AACLL,0BAAYtP,IACVgK,cAAc2C,aAAaY,QACvB3D,gBAAgBvB,aAAac,UAAUnJ,IACvC4J,gBAAgBvB,aAAaU,UAAU/I;;AAG/C,gBAAIsP,YAAYtP,GAAG;AACjB4J,8BAAgBmG,SAAS;gBACvBlP,MAAM,CAACyO,YAAYtP;gBACnB8P,UAAUb;eAFZ;;AAKF;qBACSS,cAAcF,mBAAmBvP,MAAMkP,eAAelP,GAAG;AAClE,kBAAM0P,uBACJ/F,gBAAgBpB,YAAY4G,iBAAiBnP;AAC/C,kBAAM2P,4BACH5F,cAAc2C,aAAac,QAC1BkC,wBAAwBxG,UAAUlJ,KACnC+J,cAAc2C,aAAae,MAC1BiC,wBAAwB5G,UAAU9I;AAEtC,gBAAI2P,6BAA6B,CAACR,iBAAiBpP,GAAG;AAGpD4J,8BAAgBiG,SAAS;gBACvB9O,KAAK4O;gBACLG,UAAUb;eAFZ;AAIA;;AAGF,gBAAIW,2BAA2B;AAC7BN,0BAAYrP,IAAI2J,gBAAgBpB,YAAYmH;mBACvC;AACLL,0BAAYrP,IACV+J,cAAc2C,aAAac,OACvB7D,gBAAgBpB,YAAYW,UAAUlJ,IACtC2J,gBAAgBpB,YAAYO,UAAU9I;;AAG9C,gBAAIqP,YAAYrP,GAAG;AACjB2J,8BAAgBmG,SAAS;gBACvBhP,KAAK,CAACuO,YAAYrP;gBAClB6P,UAAUb;eAFZ;;AAMF;;;AAIJ,aAAKe,WACH3S,OACA4S,IACEZ,SAAoBF,gBAAgB,KAAKpB,oBAAtB,GACnBuB,WAFoB,CAFxB;;;;EAWEU,WAAW3S,OAAc6S,aAAf;AAChB,UAAM;MAACC;QAAU,KAAKtC;AAEtBxQ,UAAMoP,eAAN;AACA0D,WAAOD,WAAD;;EAGAhB,UAAU7R,OAAD;AACf,UAAM;MAAC+S;QAAS,KAAKvC;AAErBxQ,UAAMoP,eAAN;AACA,SAAK4D,OAAL;AACAD,UAAK;;EAGCjC,aAAa9Q,OAAD;AAClB,UAAM;MAACiT;QAAY,KAAKzC;AAExBxQ,UAAMoP,eAAN;AACA,SAAK4D,OAAL;AACAC,aAAQ;;EAGFD,SAAM;AACZ,SAAKxT,UAAU+O,UAAf;AACA,SAAKoC,gBAAgBpC,UAArB;;;AA1OSgC,eA6OJ2C,aAAgD,CACrD;EACEzE,WAAW;EACXC,SAAS,CACP1O,OADO,MAAA,UAAA;QAEP;MAAC0R,gBAAgBnC;MAAsB4D;;QACvC;MAAC7S;;AAED,UAAM;MAAC2P;QAAQjQ,MAAMoT;AAErB,QAAI1B,cAAclC,MAAMtF,SAAS+F,IAA7B,GAAoC;AACtC,YAAMoD,YAAY/S,OAAOgT,cAAc/B;AAEvC,UAAI8B,aAAarT,MAAM+F,WAAWsN,WAAW;AAC3C,eAAO;;AAGTrT,YAAMoP,eAAN;AAEA+D,sBAAY,OAAZ,SAAAA,aAAe;QAACnT,OAAOA,MAAMoT;OAAjB;AAEZ,aAAO;;AAGT,WAAO;;AAvBX,CADqD;ACxOzD,SAASG,qBACPC,YADF;AAGE,SAAOC,QAAQD,cAAc,cAAcA,UAA7B;AACf;AAED,SAASE,kBACPF,YADF;AAGE,SAAOC,QAAQD,cAAc,WAAWA,UAA1B;AACf;AAaD,IAAaG,wBAAb,MAAaA;EAUX/F,YACU4C,OACAoD,SACRC,gBAAAA;;QAAAA,mBAAAA,QAAAA;AAAAA,uBAAiBjF,uBAAuB4B,MAAMxQ,MAAM+F,MAAb;;SAF/ByK,QAAAA;SACAoD,SAAAA;SAXHnD,oBAAoB;SACnBlF,WAAAA;SACAuI,YAAqB;SACrBC,qBAAAA;SACAC,YAAmC;SACnCxU,YAAAA;SACAyU,oBAAAA;SACAtD,kBAAAA;AAGE,SAAA,QAAAH;AACA,SAAA,SAAAoD;AAGR,UAAM;MAAC5T;QAASwQ;AAChB,UAAM;MAACzK;QAAU/F;AAEjB,SAAKwQ,QAAQA;AACb,SAAKoD,SAASA;AACd,SAAKrI,WAAWX,iBAAiB7E,MAAD;AAChC,SAAKkO,oBAAoB,IAAI3F,UAAU,KAAK/C,QAAnB;AACzB,SAAK/L,YAAY,IAAI8O,UAAUuF,cAAd;AACjB,SAAKlD,kBAAkB,IAAIrC,UAAUzF,UAAU9C,MAAD,CAAvB;AACvB,SAAKgO,sBAAL,uBAA0BzQ,oBAAoBtD,KAAD,MAA7C,OAAA,uBAAwDwC;AACxD,SAAKwO,cAAc,KAAKA,YAAYH,KAAK,IAAtB;AACnB,SAAK8B,aAAa,KAAKA,WAAW9B,KAAK,IAArB;AAClB,SAAKgB,YAAY,KAAKA,UAAUhB,KAAK,IAApB;AACjB,SAAKC,eAAe,KAAKA,aAAaD,KAAK,IAAvB;AACpB,SAAKqD,gBAAgB,KAAKA,cAAcrD,KAAK,IAAxB;AACrB,SAAKsD,sBAAsB,KAAKA,oBAAoBtD,KAAK,IAA9B;AAE3B,SAAKE,OAAL;;EAGMA,SAAM;AACZ,UAAM;MACJ6C,QAAAA;MACApD,OAAO;QACLpO,SAAS;UAACgS;UAAsBC;;;QAEhC;AAEJ,SAAK7U,UAAUI,IAAIgU,QAAOU,KAAKC,MAAM,KAAK5B,YAAY;MAAC6B,SAAS;KAAhE;AACA,SAAKhV,UAAUI,IAAIgU,QAAO/D,IAAI0E,MAAM,KAAK1C,SAAzC;AAEA,QAAI+B,QAAOjE,QAAQ;AACjB,WAAKnQ,UAAUI,IAAIgU,QAAOjE,OAAO4E,MAAM,KAAKzD,YAA5C;;AAGF,SAAKH,gBAAgB/Q,IAAIuP,UAAU8B,QAAQ,KAAKH,YAAhD;AACA,SAAKH,gBAAgB/Q,IAAIuP,UAAUsF,WAAWrF,cAA9C;AACA,SAAKuB,gBAAgB/Q,IAAIuP,UAAU+B,kBAAkB,KAAKJ,YAA1D;AACA,SAAKH,gBAAgB/Q,IAAIuP,UAAUuF,aAAatF,cAAhD;AACA,SAAK6E,kBAAkBrU,IAAIuP,UAAUiC,SAAS,KAAK8C,aAAnD;AAEA,QAAIE,sBAAsB;AACxB,UACEC,8BADF,QACEA,2BAA6B;QAC3BrU,OAAO,KAAKwQ,MAAMxQ;QAClBqR,YAAY,KAAKb,MAAMa;QACvBjP,SAAS,KAAKoO,MAAMpO;OAHI,GAK1B;AACA,eAAO,KAAK4O,YAAL;;AAGT,UAAI0C,kBAAkBU,oBAAD,GAAwB;AAC3C,aAAKJ,YAAY7C,WACf,KAAKH,aACLoD,qBAAqBO,KAFI;AAI3B,aAAKC,cAAcR,oBAAnB;AACA;;AAGF,UAAIb,qBAAqBa,oBAAD,GAAwB;AAC9C,aAAKQ,cAAcR,oBAAnB;AACA;;;AAIJ,SAAKpD,YAAL;;EAGMgC,SAAM;AACZ,SAAKxT,UAAU+O,UAAf;AACA,SAAKoC,gBAAgBpC,UAArB;AAIA4C,eAAW,KAAK8C,kBAAkB1F,WAAW,EAAnC;AAEV,QAAI,KAAKyF,cAAc,MAAM;AAC3Ba,mBAAa,KAAKb,SAAN;AACZ,WAAKA,YAAY;;;EAIbY,cACNpB,YACAsB,QAFmB;AAInB,UAAM;MAACxU;MAAQyU;QAAa,KAAKvE;AACjCuE,cAAUzU,QAAQkT,YAAY,KAAKO,oBAAoBe,MAA9C;;EAGH9D,cAAW;AACjB,UAAM;MAAC+C;QAAsB;AAC7B,UAAM;MAACzC;QAAW,KAAKd;AAEvB,QAAIuD,oBAAoB;AACtB,WAAKD,YAAY;AAGjB,WAAKG,kBAAkBrU,IAAIuP,UAAU6F,OAAO3F,iBAAiB;QAC3D4F,SAAS;OADX;AAKA,WAAKd,oBAAL;AAGA,WAAKF,kBAAkBrU,IACrBuP,UAAU+F,iBACV,KAAKf,mBAFP;AAKA7C,cAAQyC,kBAAD;;;EAIHpB,WAAW3S,OAAD;;AAChB,UAAM;MAAC8T;MAAWC;MAAoBvD;QAAS;AAC/C,UAAM;MACJsC;MACA1Q,SAAS;QAACgS;;QACR5D;AAEJ,QAAI,CAACuD,oBAAoB;AACvB;;AAGF,UAAMlB,eAAW,wBAAGvP,oBAAoBtD,KAAD,MAAtB,OAAA,wBAAiCwC;AAClD,UAAMuM,QAAQiD,SAAoB+B,oBAAoBlB,WAArB;AAGjC,QAAI,CAACiB,aAAaM,sBAAsB;AACtC,UAAIb,qBAAqBa,oBAAD,GAAwB;AAC9C,YACEA,qBAAqBe,aAAa,QAClCrG,oBAAoBC,OAAOqF,qBAAqBe,SAA7B,GACnB;AACA,iBAAO,KAAKrE,aAAL;;AAGT,YAAIhC,oBAAoBC,OAAOqF,qBAAqBgB,QAA7B,GAAwC;AAC7D,iBAAO,KAAKpE,YAAL;;;AAIX,UAAI0C,kBAAkBU,oBAAD,GAAwB;AAC3C,YAAItF,oBAAoBC,OAAOqF,qBAAqBe,SAA7B,GAAyC;AAC9D,iBAAO,KAAKrE,aAAL;;;AAIX,WAAK8D,cAAcR,sBAAsBrF,KAAzC;AACA;;AAGF,QAAI/O,MAAMqV,YAAY;AACpBrV,YAAMoP,eAAN;;AAGF0D,WAAOD,WAAD;;EAGAhB,YAAS;AACf,UAAM;MAACyD;MAASvC;QAAS,KAAKvC;AAE9B,SAAKwC,OAAL;AACA,QAAI,CAAC,KAAKc,WAAW;AACnBwB,cAAQ,KAAK9E,MAAMlQ,MAAZ;;AAETyS,UAAK;;EAGCjC,eAAY;AAClB,UAAM;MAACwE;MAASrC;QAAY,KAAKzC;AAEjC,SAAKwC,OAAL;AACA,QAAI,CAAC,KAAKc,WAAW;AACnBwB,cAAQ,KAAK9E,MAAMlQ,MAAZ;;AAET2S,aAAQ;;EAGFiB,cAAclU,OAAD;AACnB,QAAIA,MAAMiQ,SAASX,aAAaM,KAAK;AACnC,WAAKkB,aAAL;;;EAIIqD,sBAAmB;;AACzB,KAAA,wBAAA,KAAK5I,SAASgK,aAAd,MAAA,OAAA,SAAA,sBAA8BC,gBAA9B;;;ACtQJ,IAAM5B,SAA+B;EACnCjE,QAAQ;IAAC4E,MAAM;;EACfD,MAAM;IAACC,MAAM;;EACb1E,KAAK;IAAC0E,MAAM;;AAHuB;AAUrC,IAAakB,gBAAb,cAAmC9B,sBAAAA;EACjC/F,YAAY4C,OAAAA;AACV,UAAM;MAACxQ;QAASwQ;AAGhB,UAAMqD,iBAAiBjJ,iBAAiB5K,MAAM+F,MAAP;AAEvC,UAAMyK,OAAOoD,QAAQC,cAArB;;;AAPS4B,cAUJvC,aAAa,CAClB;EACEzE,WAAW;EACXC,SAAS,CAAA,MAAA,UAAA;QACP;MAAC0E,aAAapT;;QACd;MAACmT;;AAED,QAAI,CAACnT,MAAM0V,aAAa1V,MAAM2V,WAAW,GAAG;AAC1C,aAAO;;AAGTxC,oBAAY,OAAZ,SAAAA,aAAe;MAACnT;KAAJ;AAEZ,WAAO;;AAZX,CADkB;ACpBtB,IAAM4T,WAA+B;EACnCU,MAAM;IAACC,MAAM;;EACb1E,KAAK;IAAC0E,MAAM;;AAFuB;AAKrC,IAAKqB;CAAL,SAAKA,cAAAA;AACHA,EAAAA,aAAAA,aAAAA,YAAAA,IAAAA,CAAAA,IAAA;AACD,GAFIA,gBAAAA,cAAW,CAAA,EAAhB;AAQA,IAAaC,cAAb,cAAiClC,sBAAAA;EAC/B/F,YAAY4C,OAAAA;AACV,UAAMA,OAAOoD,UAAQhJ,iBAAiB4F,MAAMxQ,MAAM+F,MAAb,CAArC;;;AAFS8P,YAKJ3C,aAAa,CAClB;EACEzE,WAAW;EACXC,SAAS,CAAA,MAAA,UAAA;QACP;MAAC0E,aAAapT;;QACd;MAACmT;;AAED,QAAInT,MAAM2V,WAAWC,YAAYE,YAAY;AAC3C,aAAO;;AAGT3C,oBAAY,OAAZ,SAAAA,aAAe;MAACnT;KAAJ;AAEZ,WAAO;;AAZX,CADkB;AClBtB,IAAM4T,WAA+B;EACnCjE,QAAQ;IAAC4E,MAAM;;EACfD,MAAM;IAACC,MAAM;;EACb1E,KAAK;IAAC0E,MAAM;;AAHuB;AAUrC,IAAawB,cAAb,cAAiCpC,sBAAAA;EAC/B/F,YAAY4C,OAAAA;AACV,UAAMA,OAAOoD,QAAb;;EAuBU,OAALoC,QAAK;AAIVnL,WAAO8D,iBAAiBiF,SAAOU,KAAKC,MAAMtS,OAAM;MAC9CgT,SAAS;MACTT,SAAS;KAFX;AAKA,WAAO,SAASyB,WAAT;AACLpL,aAAO2D,oBAAoBoF,SAAOU,KAAKC,MAAMtS,KAA7C;;AAKF,aAASA,QAAT;IAAA;;;AAxCS8T,YAKJ7C,aAAa,CAClB;EACEzE,WAAW;EACXC,SAAS,CAAA,MAAA,UAAA;QACP;MAAC0E,aAAapT;;QACd;MAACmT;;AAED,UAAM;MAAC+C;QAAWlW;AAElB,QAAIkW,QAAQ7R,SAAS,GAAG;AACtB,aAAO;;AAGT8O,oBAAY,OAAZ,SAAAA,aAAe;MAACnT;KAAJ;AAEZ,WAAO;;AAdX,CADkB;IChBVmW;CAAZ,SAAYA,sBAAAA;AACVA,EAAAA,qBAAAA,qBAAAA,SAAAA,IAAAA,CAAAA,IAAA;AACAA,EAAAA,qBAAAA,qBAAAA,eAAAA,IAAAA,CAAAA,IAAA;AACD,GAHWA,wBAAAA,sBAAmB,CAAA,EAA/B;AAmCA,IAAYC;CAAZ,SAAYA,iBAAAA;AACVA,EAAAA,gBAAAA,gBAAAA,WAAAA,IAAAA,CAAAA,IAAA;AACAA,EAAAA,gBAAAA,gBAAAA,mBAAAA,IAAAA,CAAAA,IAAA;AACD,GAHWA,mBAAAA,iBAAc,CAAA,EAA1B;AAUA,SAAgBC,gBAAAA,MAAAA;MAAgB;IAC9B5J;IACA4G,YAAY8C,oBAAoBG;IAChCC;IACAC;IACAC;IACAC,WAAW;IACXC,QAAQP,eAAeQ;IACvBhQ;IACAuG;IACA0J;IACA9H;IACAlC;;AAEA,QAAMiK,eAAeC,gBAAgB;IAAChI;IAAOiI,UAAU,CAACP;GAApB;AACpC,QAAM,CAACQ,uBAAuBC,uBAAxB,IAAmDC,YAAW;AACpE,QAAMC,kBAAcC,sBAAoB;IAAC1U,GAAG;IAAGC,GAAG;GAAxB;AAC1B,QAAM0U,sBAAkBD,sBAAwB;IAAC1U,GAAG;IAAGC,GAAG;GAA5B;AAC9B,QAAMQ,WAAO5B,uBAAQ,MAAA;AACnB,YAAQ6R,WAAR;MACE,KAAK8C,oBAAoBG;AACvB,eAAO1P,qBACH;UACElD,KAAKkD,mBAAmBhE;UACxBuD,QAAQS,mBAAmBhE;UAC3BY,MAAMoD,mBAAmBjE;UACzBsD,OAAOW,mBAAmBjE;YAE5B;MACN,KAAKwT,oBAAoBoB;AACvB,eAAOf;;KAEV,CAACnD,WAAWmD,cAAc5P,kBAA1B,CAdiB;AAepB,QAAM4Q,yBAAqBH,sBAAuB,IAAjB;AACjC,QAAMI,iBAAa9X,2BAAY,MAAA;AAC7B,UAAM4M,kBAAkBiL,mBAAmBjG;AAE3C,QAAI,CAAChF,iBAAiB;AACpB;;AAGF,UAAMvB,aAAaoM,YAAY7F,QAAQ5O,IAAI2U,gBAAgB/F,QAAQ5O;AACnE,UAAMwI,YAAYiM,YAAY7F,QAAQ3O,IAAI0U,gBAAgB/F,QAAQ3O;AAElE2J,oBAAgBmG,SAAS1H,YAAYG,SAArC;KACC,CAAA,CAX2B;AAY9B,QAAMuM,gCAA4BlW,uBAChC,MACEmV,UAAUP,eAAeQ,YACrB,CAAC,GAAGzJ,mBAAJ,EAAyBwK,QAAzB,IACAxK,qBACN,CAACwJ,OAAOxJ,mBAAR,CALuC;AAQzC/N;IACE,MAAA;AACE,UAAI,CAACqX,WAAW,CAACtJ,oBAAoB9I,UAAU,CAACjB,MAAM;AACpD8T,gCAAuB;AACvB;;AAGF,iBAAW3K,mBAAmBmL,2BAA2B;AACvD,aAAInB,aAAS,OAAT,SAAAA,UAAYhK,eAAH,OAAwB,OAAO;AAC1C;;AAGF,cAAM9G,QAAQ0H,oBAAoB9E,QAAQkE,eAA5B;AACd,cAAMC,sBAAsBqK,wBAAwBpR,KAAD;AAEnD,YAAI,CAAC+G,qBAAqB;AACxB;;AAGF,cAAM;UAACG;UAAWC;YAASN,2BACzBC,iBACAC,qBACApJ,MACAqJ,cACAI,SALmD;AAQrD,mBAAWiB,QAAQ,CAAC,KAAK,GAAN,GAAqB;AACtC,cAAI,CAACgJ,aAAahJ,IAAD,EAAOnB,UAAUmB,IAAD,CAA5B,GAAkD;AACrDlB,kBAAMkB,IAAD,IAAS;AACdnB,sBAAUmB,IAAD,IAAS;;;AAItB,YAAIlB,MAAMjK,IAAI,KAAKiK,MAAMhK,IAAI,GAAG;AAC9BsU,kCAAuB;AAEvBM,6BAAmBjG,UAAUhF;AAC7B0K,gCAAsBQ,YAAYf,QAAb;AAErBU,sBAAY7F,UAAU3E;AACtB0K,0BAAgB/F,UAAU5E;AAE1B;;;AAIJyK,kBAAY7F,UAAU;QAAC5O,GAAG;QAAGC,GAAG;;AAChC0U,sBAAgB/F,UAAU;QAAC5O,GAAG;QAAGC,GAAG;;AACpCsU,8BAAuB;;;IAGzB;MACEzK;MACAgL;MACAlB;MACAW;MACAT;MACAC;;MAEAkB,KAAKC,UAAUzU,IAAf;;MAEAwU,KAAKC,UAAUf,YAAf;MACAG;MACA9J;MACAuK;MACAb;;MAEAe,KAAKC,UAAUhL,SAAf;IAhBF;EApDO;AAuEV;AAOD,IAAMiL,sBAAoC;EACxCnV,GAAG;IAAC,CAAC0I,UAAUyB,QAAX,GAAsB;IAAO,CAACzB,UAAU2B,OAAX,GAAqB;;EACtDpK,GAAG;IAAC,CAACyI,UAAUyB,QAAX,GAAsB;IAAO,CAACzB,UAAU2B,OAAX,GAAqB;;AAFd;AAK1C,SAAS+J,gBAAT,OAAA;MAAyB;IACvBhI;IACAiI;;AAKA,QAAMe,gBAAgBC,YAAYjJ,KAAD;AAEjC,SAAOkJ,YACJC,oBAAD;AACE,QAAIlB,YAAY,CAACe,iBAAiB,CAACG,gBAAgB;AAEjD,aAAOJ;;AAGT,UAAMnL,YAAY;MAChBhK,GAAGK,KAAKmV,KAAKpJ,MAAMpM,IAAIoV,cAAcpV,CAAlC;MACHC,GAAGI,KAAKmV,KAAKpJ,MAAMnM,IAAImV,cAAcnV,CAAlC;;AAIL,WAAO;MACLD,GAAG;QACD,CAAC0I,UAAUyB,QAAX,GACEoL,eAAevV,EAAE0I,UAAUyB,QAA3B,KAAwCH,UAAUhK,MAAM;QAC1D,CAAC0I,UAAU2B,OAAX,GACEkL,eAAevV,EAAE0I,UAAU2B,OAA3B,KAAuCL,UAAUhK,MAAM;;MAE3DC,GAAG;QACD,CAACyI,UAAUyB,QAAX,GACEoL,eAAetV,EAAEyI,UAAUyB,QAA3B,KAAwCH,UAAU/J,MAAM;QAC1D,CAACyI,UAAU2B,OAAX,GACEkL,eAAetV,EAAEyI,UAAU2B,OAA3B,KAAuCL,UAAU/J,MAAM;;;KAI/D,CAACoU,UAAUjI,OAAOgJ,aAAlB,CA5BgB;AA8BnB;SCjOeK,cACdC,gBACA9X,IAAAA;AAEA,QAAM+X,gBAAgB/X,MAAM,OAAO8X,eAAevT,IAAIvE,EAAnB,IAAyB+P;AAC5D,QAAMlH,OAAOkP,gBAAgBA,cAAclP,KAAKmI,UAAU;AAE1D,SAAO0G,YACJM,gBAAD;;AACE,QAAIhY,MAAM,MAAM;AACd,aAAO;;AAMT,YAAA,OAAO6I,QAAP,OAAOA,OAAQmP,eAAf,OAAA,OAA6B;KAE/B,CAACnP,MAAM7I,EAAP,CAXgB;AAanB;SCjBeiY,qBACdlW,SACAmW,qBAAAA;AAKA,aAAOjX,uBACL,MACEc,QAAQgD,OAA2B,CAACC,aAAapD,WAAd;AACjC,UAAM;MAACA,QAAQuW;QAAUvW;AAEzB,UAAMwW,mBAAmBD,OAAOxF,WAAW0F,IAAKvF,gBAAe;MAC7D5E,WAAW4E,UAAU5E;MACrBC,SAAS+J,oBAAoBpF,UAAU3E,SAASvM,MAApB;MAFL;AAKzB,WAAO,CAAC,GAAGoD,aAAa,GAAGoT,gBAApB;KACN,CAAA,CATH,GAUF,CAACrW,SAASmW,mBAAV,CAZY;AAcf;IChBWI;CAAZ,SAAYA,oBAAAA;AACVA,EAAAA,mBAAAA,mBAAAA,QAAAA,IAAAA,CAAAA,IAAA;AACAA,EAAAA,mBAAAA,mBAAAA,gBAAAA,IAAAA,CAAAA,IAAA;AACAA,EAAAA,mBAAAA,mBAAAA,eAAAA,IAAAA,CAAAA,IAAA;AACD,GAJWA,sBAAAA,oBAAiB,CAAA,EAA7B;AAMA,IAAYC;CAAZ,SAAYA,qBAAAA;AACVA,EAAAA,oBAAAA,WAAAA,IAAA;AACD,GAFWA,uBAAAA,qBAAkB,CAAA,EAA9B;AAYA,IAAMC,eAAwB,oBAAIC,IAAJ;AAE9B,SAAgBC,sBACdC,YAAAA,MAAAA;MACA;IAACC;IAAUC;IAAcC;;AAEzB,QAAM,CAACC,OAAOC,QAAR,QAAoB9Z,wBAAoC,IAA5B;AAClC,QAAM;IAAC+Z;IAAWjM;IAASkM;MAAYJ;AACvC,QAAMK,oBAAgBrC,sBAAO6B,UAAD;AAC5B,QAAMlC,WAAW2C,WAAU;AAC3B,QAAMC,cAAcC,eAAe7C,QAAD;AAClC,QAAM8C,iCAA6Bna,2BACjC,SAACoa,KAAD;QAACA,QAAAA,QAAAA;AAAAA,YAA0B,CAAA;;AACzB,QAAIH,YAAYrI,SAAS;AACvB;;AAGFgI,aAAU1X,WAAD;AACP,UAAIA,UAAU,MAAM;AAClB,eAAOkY;;AAGT,aAAOlY,MAAMmY,OAAOD,IAAIxX,OAAQhC,QAAO,CAACsB,MAAMqI,SAAS3J,EAAf,CAApB,CAAb;KALD;KAQV,CAACqZ,WAAD,CAd4C;AAgB9C,QAAM5F,gBAAYqD,sBAA8B,IAAxB;AACxB,QAAM3S,iBAAiBuT,YACpBgC,mBAAD;AACE,QAAIjD,YAAY,CAACmC,UAAU;AACzB,aAAOJ;;AAGT,QACE,CAACkB,iBACDA,kBAAkBlB,gBAClBW,cAAcnI,YAAY2H,cAC1BI,SAAS,MACT;AACA,YAAMV,MAAe,oBAAII,IAAJ;AAErB,eAASlY,aAAaoY,YAAY;AAChC,YAAI,CAACpY,WAAW;AACd;;AAGF,YACEwY,SACAA,MAAMjV,SAAS,KACf,CAACiV,MAAMpP,SAASpJ,UAAUP,EAAzB,KACDO,UAAUsC,KAAKmO,SACf;AAEAqH,cAAIsB,IAAIpZ,UAAUP,IAAIO,UAAUsC,KAAKmO,OAArC;AACA;;AAGF,cAAMnI,OAAOtI,UAAUsI,KAAKmI;AAC5B,cAAMnO,OAAOgG,OAAO,IAAIuE,KAAKJ,QAAQnE,IAAD,GAAQA,IAAxB,IAAgC;AAEpDtI,kBAAUsC,KAAKmO,UAAUnO;AAEzB,YAAIA,MAAM;AACRwV,cAAIsB,IAAIpZ,UAAUP,IAAI6C,IAAtB;;;AAIJ,aAAOwV;;AAGT,WAAOqB;KAET,CAACf,YAAYI,OAAOH,UAAUnC,UAAUzJ,OAAxC,CA7CgC;AAgDlCnO,+BAAU,MAAA;AACRsa,kBAAcnI,UAAU2H;KACvB,CAACA,UAAD,CAFM;AAIT9Z;IACE,MAAA;AACE,UAAI4X,UAAU;AACZ;;AAGF8C,iCAA0B;;;IAG5B,CAACX,UAAUnC,QAAX;EATO;AAYT5X;IACE,MAAA;AACE,UAAIka,SAASA,MAAMjV,SAAS,GAAG;AAC7BkV,iBAAS,IAAD;;;;IAIZ,CAAC3B,KAAKC,UAAUyB,KAAf,CAAD;EAPO;AAUTla;IACE,MAAA;AACE,UACE4X,YACA,OAAOwC,cAAc,YACrBxF,UAAUzC,YAAY,MACtB;AACA;;AAGFyC,gBAAUzC,UAAUJ,WAAW,MAAA;AAC7B2I,mCAA0B;AAC1B9F,kBAAUzC,UAAU;SACnBiI,SAH2B;;;IAMhC,CAACA,WAAWxC,UAAU8C,4BAA4B,GAAGV,YAArD;EAhBO;AAmBT,SAAO;IACL1U;IACAoV;IACAK,oBAAoBb,SAAS;;AAG/B,WAASK,aAAT;AACE,YAAQF,UAAR;MACE,KAAKZ,kBAAkBuB;AACrB,eAAO;MACT,KAAKvB,kBAAkBwB;AACrB,eAAOlB;MACT;AACE,eAAO,CAACA;;;AAGf;SCpKemB,gBAIdzY,OACA0Y,WAAAA;AAEA,SAAOtC,YACJgC,mBAAD;AACE,QAAI,CAACpY,OAAO;AACV,aAAO;;AAGT,QAAIoY,eAAe;AACjB,aAAOA;;AAGT,WAAO,OAAOM,cAAc,aAAaA,UAAU1Y,KAAD,IAAUA;KAE9D,CAAC0Y,WAAW1Y,KAAZ,CAZgB;AAcnB;SCtBe2Y,eACdpR,MACAmE,SAAAA;AAEA,SAAO+M,gBAAgBlR,MAAMmE,OAAP;AACvB;ACID,SAAgBkN,oBAAAA,MAAAA;MAAoB;IAACC;IAAU1D;;AAC7C,QAAM2D,kBAAkBC,SAASF,QAAD;AAChC,QAAMG,uBAAmBrZ,uBAAQ,MAAA;AAC/B,QACEwV,YACA,OAAOnM,WAAW,eAClB,OAAOA,OAAOiQ,qBAAqB,aACnC;AACA,aAAOxK;;AAGT,UAAM;MAACwK;QAAoBjQ;AAE3B,WAAO,IAAIiQ,iBAAiBH,eAArB;KACN,CAACA,iBAAiB3D,QAAlB,CAZ6B;AAchC5X,+BAAU,MAAA;AACR,WAAO,MAAMyb,oBAAN,OAAA,SAAMA,iBAAkBE,WAAlB;KACZ,CAACF,gBAAD,CAFM;AAIT,SAAOA;AACR;ACrBD,SAAgBG,kBAAAA,MAAAA;MAAkB;IAACN;IAAU1D;;AAC3C,QAAMiE,eAAeL,SAASF,QAAD;AAC7B,QAAMQ,qBAAiB1Z;IACrB,MAAA;AACE,UACEwV,YACA,OAAOnM,WAAW,eAClB,OAAOA,OAAOsQ,mBAAmB,aACjC;AACA,eAAO7K;;AAGT,YAAM;QAAC6K;UAAkBtQ;AAEzB,aAAO,IAAIsQ,eAAeF,YAAnB;;;IAGT,CAACjE,QAAD;EAf4B;AAkB9B5X,+BAAU,MAAA;AACR,WAAO,MAAM8b,kBAAN,OAAA,SAAMA,eAAgBH,WAAhB;KACZ,CAACG,cAAD,CAFM;AAIT,SAAOA;AACR;AC5BD,SAASE,eAAezS,SAAxB;AACE,SAAO,IAAIgF,KAAKjF,cAAcC,OAAD,GAAWA,OAAjC;AACR;AAED,SAAgB0S,QACd1S,SACA4E,SACA+N,cAAAA;MADA/N,YAAAA,QAAAA;AAAAA,cAAgD6N;;AAGhD,QAAM,CAAChY,MAAMmY,OAAP,QAAkB9b,wBAA4B,IAApB;AAEhC,WAAS+b,cAAT;AACED,YAASE,iBAAD;AACN,UAAI,CAAC9S,SAAS;AACZ,eAAO;;AAGT,UAAIA,QAAQ+S,gBAAgB,OAAO;AAAA,YAAA;AAGjC,gBAAA,OAAOD,eAAP,OAAOA,cAAeH,iBAAtB,OAAA,OAAsC;;AAGxC,YAAMK,UAAUpO,QAAQ5E,OAAD;AAEvB,UAAIiP,KAAKC,UAAU4D,WAAf,MAAgC7D,KAAKC,UAAU8D,OAAf,GAAyB;AAC3D,eAAOF;;AAGT,aAAOE;KAjBF;;AAqBT,QAAMd,mBAAmBJ,oBAAoB;IAC3CC,SAASkB,SAAD;AACN,UAAI,CAACjT,SAAS;AACZ;;AAGF,iBAAWkT,UAAUD,SAAS;AAC5B,cAAM;UAAC7b;UAAMgG;YAAU8V;AAEvB,YACE9b,SAAS,eACTgG,kBAAkB+V,eAClB/V,OAAOgW,SAASpT,OAAhB,GACA;AACA6S,sBAAW;AACX;;;;GAfoC;AAoB5C,QAAMN,iBAAiBF,kBAAkB;IAACN,UAAUc;GAAZ;AAExCQ,4BAA0B,MAAA;AACxBR,gBAAW;AAEX,QAAI7S,SAAS;AACXuS,wBAAc,OAAd,SAAAA,eAAgBe,QAAQtT,OAAxB;AACAkS,0BAAgB,OAAhB,SAAAA,iBAAkBoB,QAAQ1Q,SAAS2Q,MAAM;QACvCC,WAAW;QACXC,SAAS;OAFX;WAIK;AACLlB,wBAAc,OAAd,SAAAA,eAAgBH,WAAhB;AACAF,0BAAgB,OAAhB,SAAAA,iBAAkBE,WAAlB;;KAED,CAACpS,OAAD,CAbsB;AAezB,SAAOvF;AACR;SC3EeiZ,aAAajZ,MAAAA;AAC3B,QAAMkZ,cAAchC,gBAAgBlX,IAAD;AAEnC,SAAO+D,aAAa/D,MAAMkZ,WAAP;AACpB;ACJD,IAAMvD,iBAA0B,CAAA;AAEhC,SAAgBwD,uBAAuBnT,MAAAA;AACrC,QAAMoT,mBAAenF,sBAAOjO,IAAD;AAE3B,QAAMqT,YAAYxE,YACfgC,mBAAD;AACE,QAAI,CAAC7Q,MAAM;AACT,aAAO2P;;AAGT,QACEkB,iBACAA,kBAAkBlB,kBAClB3P,QACAoT,aAAajL,WACbnI,KAAKiB,eAAemS,aAAajL,QAAQlH,YACzC;AACA,aAAO4P;;AAGT,WAAOrQ,uBAAuBR,IAAD;KAE/B,CAACA,IAAD,CAlB2B;AAqB7BhK,+BAAU,MAAA;AACRod,iBAAajL,UAAUnI;KACtB,CAACA,IAAD,CAFM;AAIT,SAAOqT;AACR;SCvBeC,iBAAiBC,UAAAA;AAC/B,QAAM,CACJC,mBACAC,oBAFI,QAGFpd,wBAAmC,IAA3B;AACZ,QAAMqd,mBAAezF,sBAAOsF,QAAD;AAG3B,QAAMI,mBAAepd,2BAAaK,WAAD;AAC/B,UAAMiK,mBAAmBO,qBAAqBxK,MAAM+F,MAAP;AAE7C,QAAI,CAACkE,kBAAkB;AACrB;;AAGF4S,yBAAsBD,CAAAA,uBAAD;AACnB,UAAI,CAACA,oBAAmB;AACtB,eAAO;;AAGTA,MAAAA,mBAAkB1C,IAChBjQ,kBACAmB,qBAAqBnB,gBAAD,CAFtB;AAKA,aAAO,IAAI+O,IAAI4D,kBAAR;KAVW;KAYnB,CAAA,CAnB6B;AAqBhCxd,+BAAU,MAAA;AACR,UAAM4d,mBAAmBF,aAAavL;AAEtC,QAAIoL,aAAaK,kBAAkB;AACjCC,cAAQD,gBAAD;AAEP,YAAME,UAAUP,SACb/D,IAAKjQ,aAAD;AACH,cAAMwU,oBAAoB3S,qBAAqB7B,OAAD;AAE9C,YAAIwU,mBAAmB;AACrBA,4BAAkBxO,iBAAiB,UAAUoO,cAAc;YACzDvI,SAAS;WADX;AAIA,iBAAO,CACL2I,mBACA/R,qBAAqB+R,iBAAD,CAFf;;AAMT,eAAO;OAfK,EAiBb5a,OAEGuD,WAIGA,SAAS,IAvBF;AA0BhB+W,2BAAqBK,QAAQ7Y,SAAS,IAAI2U,IAAIkE,OAAR,IAAmB,IAArC;AAEpBJ,mBAAavL,UAAUoL;;AAGzB,WAAO,MAAA;AACLM,cAAQN,QAAD;AACPM,cAAQD,gBAAD;;AAGT,aAASC,QAAQN,WAAjB;AACEA,MAAAA,UAAS1c,QAAS0I,aAAD;AACf,cAAMwU,oBAAoB3S,qBAAqB7B,OAAD;AAE9CwU,6BAAiB,OAAjB,SAAAA,kBAAmB3O,oBAAoB,UAAUuO,YAAjD;OAHF;;KAMD,CAACA,cAAcJ,QAAf,CAjDM;AAmDT,aAAOnb,uBAAQ,MAAA;AACb,QAAImb,SAAStY,QAAQ;AACnB,aAAOuY,oBACHQ,MAAMC,KAAKT,kBAAkBU,OAAlB,CAAX,EAAuChY,OACrC,CAACkC,KAAKqL,gBAAgBjT,IAAI4H,KAAKqL,WAAN,GACzBrQ,kBAFF,IAIA0K,iBAAiByP,QAAD;;AAGtB,WAAOna;KACN,CAACma,UAAUC,iBAAX,CAXW;AAYf;SCpGeW,sBACd1P,eACAuL,cAAAA;MAAAA,iBAAAA,QAAAA;AAAAA,mBAAsB,CAAA;;AAEtB,QAAMoE,2BAAuBnG,sBAA2B,IAArB;AAEnCjY;IACE,MAAA;AACEoe,2BAAqBjM,UAAU;;;IAGjC6H;EALO;AAQTha,+BAAU,MAAA;AACR,UAAMqe,mBAAmB5P,kBAAkBrL;AAE3C,QAAIib,oBAAoB,CAACD,qBAAqBjM,SAAS;AACrDiM,2BAAqBjM,UAAU1D;;AAGjC,QAAI,CAAC4P,oBAAoBD,qBAAqBjM,SAAS;AACrDiM,2BAAqBjM,UAAU;;KAEhC,CAAC1D,aAAD,CAVM;AAYT,SAAO2P,qBAAqBjM,UACxBmM,SAAS7P,eAAe2P,qBAAqBjM,OAArC,IACR/O;AACL;SC7Bemb,eAAerb,SAAAA;AAC7BlD;IACE,MAAA;AACE,UAAI,CAACqL,WAAW;AACd;;AAGF,YAAMmT,cAActb,QAAQsW,IAAI,UAAA;AAAA,YAAC;UAACzW;YAAF;AAAA,eAAcA,OAAO6T,SAArB,OAAA,SAAc7T,OAAO6T,MAAP;OAA1B;AAEpB,aAAO,MAAA;AACL,mBAAWC,YAAY2H,aAAa;AAClC3H,sBAAQ,OAAR,SAAAA,SAAQ;;;;;;IAMd3T,QAAQsW,IAAI,WAAA;AAAA,UAAC;QAACzW;UAAF;AAAA,aAAcA;KAA1B;EAhBO;AAkBV;SCXe0b,sBACdre,WACAe,IAAAA;AAEA,aAAOiB,uBAAQ,MAAA;AACb,WAAOhC,UAAU8F,OACf,CAACkC,KAAD,SAAA;UAAM;QAACiH;QAAWC;;AAChBlH,UAAIiH,SAAD,IAAezO,WAAD;AACf0O,gBAAQ1O,OAAOO,EAAR;;AAGT,aAAOiH;OAET,CAAA,CARK;KAUN,CAAChI,WAAWe,EAAZ,CAXW;AAYf;SCzBeud,cAAcnV,SAAAA;AAC5B,aAAOnH,uBAAQ,MAAOmH,UAAUK,oBAAoBL,OAAD,IAAY,MAAO,CACpEA,OADoE,CAAxD;AAGf;ACED,IAAMoQ,iBAAuB,CAAA;AAE7B,SAAgBgF,SACdpB,UACApP,SAAAA;MAAAA,YAAAA,QAAAA;AAAAA,cAA4C7E;;AAE5C,QAAM,CAACsV,YAAD,IAAiBrB;AACvB,QAAMsB,aAAaH,cACjBE,eAAenV,UAAUmV,YAAD,IAAiB,IADX;AAGhC,QAAM,CAACE,OAAOC,QAAR,QAAoB1e,wBAAuBsZ,cAAf;AAElC,WAASqF,eAAT;AACED,aAAS,MAAA;AACP,UAAI,CAACxB,SAAStY,QAAQ;AACpB,eAAO0U;;AAGT,aAAO4D,SAAS/D,IAAKjQ,aACnB2C,2BAA2B3C,OAAD,IACrBsV,aACD,IAAItQ,KAAKJ,QAAQ5E,OAAD,GAAWA,OAA3B,CAHC;KALD;;AAaV,QAAMuS,iBAAiBF,kBAAkB;IAACN,UAAU0D;GAAZ;AAExCpC,4BAA0B,MAAA;AACxBd,sBAAc,OAAd,SAAAA,eAAgBH,WAAhB;AACAqD,iBAAY;AACZzB,aAAS1c,QAAS0I,aAAYuS,kBAAb,OAAA,SAAaA,eAAgBe,QAAQtT,OAAxB,CAA9B;KACC,CAACgU,QAAD,CAJsB;AAMzB,SAAOuB;AACR;SC3CeG,kBACdjV,MAAAA;AAEA,MAAI,CAACA,MAAM;AACT,WAAO;;AAGT,MAAIA,KAAKkV,SAASja,SAAS,GAAG;AAC5B,WAAO+E;;AAET,QAAMmV,aAAanV,KAAKkV,SAAS,CAAd;AAEnB,SAAOnU,cAAcoU,UAAD,IAAeA,aAAanV;AACjD;SCHeoV,wBAAAA,MAAAA;MAAwB;IACtCjR;;AAEA,QAAM,CAACnK,MAAMmY,OAAP,QAAkB9b,wBAA4B,IAApB;AAChC,QAAMwb,mBAAetb,2BAClBud,aAAD;AACE,eAAW;MAACnX;SAAWmX,SAAS;AAC9B,UAAI/S,cAAcpE,MAAD,GAAU;AACzBwV,gBAASnY,CAAAA,UAAD;AACN,gBAAMuY,UAAUpO,QAAQxH,MAAD;AAEvB,iBAAO3C,QACH;YAAC,GAAGA;YAAMK,OAAOkY,QAAQlY;YAAOE,QAAQgY,QAAQhY;cAChDgY;SALC;AAOP;;;KAIN,CAACpO,OAAD,CAf8B;AAiBhC,QAAM2N,iBAAiBF,kBAAkB;IAACN,UAAUO;GAAZ;AACxC,QAAMwD,uBAAmB9e,2BACtBgJ,aAAD;AACE,UAAMS,OAAOiV,kBAAkB1V,OAAD;AAE9BuS,sBAAc,OAAd,SAAAA,eAAgBH,WAAhB;AAEA,QAAI3R,MAAM;AACR8R,wBAAc,OAAd,SAAAA,eAAgBe,QAAQ7S,IAAxB;;AAGFmS,YAAQnS,OAAOmE,QAAQnE,IAAD,IAAS,IAAxB;KAET,CAACmE,SAAS2N,cAAV,CAZkC;AAcpC,QAAM,CAACwD,SAASC,MAAV,IAAoBC,WAAWH,gBAAD;AAEpC,aAAOjd,uBACL,OAAO;IACLkd;IACAtb;IACAub;MAEF,CAACvb,MAAMsb,SAASC,MAAhB,CANY;AAQf;AC9CM,IAAME,iBAAiB,CAC5B;EAAC1c,QAAQsT;EAAerT,SAAS,CAAA;AAAjC,GACA;EAACD,QAAQoO;EAAgBnO,SAAS,CAAA;AAAlC,CAF4B;AAKvB,IAAM0c,cAAuB;EAACvN,SAAS,CAAA;AAAV;AAE7B,IAAMwN,gCAAsE;EACjF5e,WAAW;IACToN,SAASxE;;EAEXiW,WAAW;IACTzR,SAASxE;IACT0Q,UAAUZ,kBAAkBoG;IAC5BzF,WAAWV,mBAAmBoG;;EAEhCC,aAAa;IACX5R,SAAS7E;;AAVsE;ICdtE0W,uCAA+BpG,IAAAA;EAI1ClU,IAAIvE,IAAD;;AACD,WAAOA,MAAM,QAAN,aAAa,MAAMuE,IAAIvE,EAAV,MAAb,OAAA,aAA8B+P,SAAYA;;EAGnD+O,UAAO;AACL,WAAOjC,MAAMC,KAAK,KAAKC,OAAL,CAAX;;EAGTgC,aAAU;AACR,WAAO,KAAKD,QAAL,EAAe9c,OAAO,UAAA;AAAA,UAAC;QAACyU;UAAF;AAAA,aAAgB,CAACA;KAAvC;;EAGTuI,WAAWhf,IAAD;;AACR,YAAA,yBAAA,YAAO,KAAKuE,IAAIvE,EAAT,MAAP,OAAA,SAAO,UAAc6I,KAAKmI,YAA1B,OAAA,wBAAqCjB;;;ACflC,IAAMkP,uBAAgD;EAC3DC,gBAAgB;EAChBnf,QAAQ;EACR+Q,YAAY;EACZqO,gBAAgB;EAChBvb,YAAY;EACZwb,mBAAmB;EACnBtH,gBAAgB,oBAAIW,IAAJ;EAChBtU,gBAAgB,oBAAIsU,IAAJ;EAChBrU,qBAAqB,IAAIya,uBAAJ;EACrB3e,MAAM;EACN0e,aAAa;IACXT,SAAS;MACPnN,SAAS;;IAEXnO,MAAM;IACNub,QAAQ1c;;EAEVkL,qBAAqB,CAAA;EACrB0J,yBAAyB,CAAA;EACzB+I,wBAAwBb;EACxBjF,4BAA4B7X;EAC5Bgc,YAAY;EACZ9D,oBAAoB;AAvBuC;AA0BtD,IAAM0F,yBAAoD;EAC/DJ,gBAAgB;EAChBvM,YAAY,CAAA;EACZ5S,QAAQ;EACRof,gBAAgB;EAChBI,mBAAmB;IACjB3f,WAAW;;EAEbL,UAAUmC;EACVoW,gBAAgB,oBAAIW,IAAJ;EAChBvY,MAAM;EACNqZ,4BAA4B7X;AAXmC;AAc1D,IAAM8d,sBAAkBhhB,6BAC7B8gB,sBAD0C;AAIrC,IAAMG,oBAAgBjhB,6BAC3BygB,oBADwC;SC/C1BS,kBAAAA;AACd,SAAO;IACL9f,WAAW;MACTG,QAAQ;MACRyT,oBAAoB;QAACpR,GAAG;QAAGC,GAAG;;MAC9Bsd,OAAO,oBAAIlH,IAAJ;MACPmH,WAAW;QAACxd,GAAG;QAAGC,GAAG;;;IAEvBoc,WAAW;MACT9F,YAAY,IAAIkG,uBAAJ;;;AAGjB;AAED,SAAgBgB,QAAQC,OAAcC,QAAAA;AACpC,UAAQA,OAAOvgB,MAAf;IACE,KAAKiC,OAAOyS;AACV,aAAO;QACL,GAAG4L;QACHlgB,WAAW;UACT,GAAGkgB,MAAMlgB;UACT4T,oBAAoBuM,OAAOvM;UAC3BzT,QAAQggB,OAAOhgB;;;IAGrB,KAAK0B,OAAOue;AACV,UAAIF,MAAMlgB,UAAUG,UAAU,MAAM;AAClC,eAAO+f;;AAGT,aAAO;QACL,GAAGA;QACHlgB,WAAW;UACT,GAAGkgB,MAAMlgB;UACTggB,WAAW;YACTxd,GAAG2d,OAAOzN,YAAYlQ,IAAI0d,MAAMlgB,UAAU4T,mBAAmBpR;YAC7DC,GAAG0d,OAAOzN,YAAYjQ,IAAIyd,MAAMlgB,UAAU4T,mBAAmBnR;;;;IAIrE,KAAKZ,OAAOwe;IACZ,KAAKxe,OAAOye;AACV,aAAO;QACL,GAAGJ;QACHlgB,WAAW;UACT,GAAGkgB,MAAMlgB;UACTG,QAAQ;UACRyT,oBAAoB;YAACpR,GAAG;YAAGC,GAAG;;UAC9Bud,WAAW;YAACxd,GAAG;YAAGC,GAAG;;;;IAI3B,KAAKZ,OAAO0e,mBAAmB;AAC7B,YAAM;QAAC/X;UAAW2X;AAClB,YAAM;QAAC/f;UAAMoI;AACb,YAAMuQ,aAAa,IAAIkG,uBAAuBiB,MAAMrB,UAAU9F,UAA3C;AACnBA,iBAAWgB,IAAI3Z,IAAIoI,OAAnB;AAEA,aAAO;QACL,GAAG0X;QACHrB,WAAW;UACT,GAAGqB,MAAMrB;UACT9F;;;;IAKN,KAAKlX,OAAO2e,sBAAsB;AAChC,YAAM;QAACpgB;QAAI0N,KAAAA;QAAK+I;UAAYsJ;AAC5B,YAAM3X,UAAU0X,MAAMrB,UAAU9F,WAAWpU,IAAIvE,EAA/B;AAEhB,UAAI,CAACoI,WAAWsF,SAAQtF,QAAQsF,KAAK;AACnC,eAAOoS;;AAGT,YAAMnH,aAAa,IAAIkG,uBAAuBiB,MAAMrB,UAAU9F,UAA3C;AACnBA,iBAAWgB,IAAI3Z,IAAI;QACjB,GAAGoI;QACHqO;OAFF;AAKA,aAAO;QACL,GAAGqJ;QACHrB,WAAW;UACT,GAAGqB,MAAMrB;UACT9F;;;;IAKN,KAAKlX,OAAO4e,qBAAqB;AAC/B,YAAM;QAACrgB;QAAI0N,KAAAA;UAAOqS;AAClB,YAAM3X,UAAU0X,MAAMrB,UAAU9F,WAAWpU,IAAIvE,EAA/B;AAEhB,UAAI,CAACoI,WAAWsF,SAAQtF,QAAQsF,KAAK;AACnC,eAAOoS;;AAGT,YAAMnH,aAAa,IAAIkG,uBAAuBiB,MAAMrB,UAAU9F,UAA3C;AACnBA,iBAAWrZ,OAAOU,EAAlB;AAEA,aAAO;QACL,GAAG8f;QACHrB,WAAW;UACT,GAAGqB,MAAMrB;UACT9F;;;;IAKN,SAAS;AACP,aAAOmH;;;AAGZ;SCzGeQ,aAAAA,MAAAA;MAAa;IAAC7J;;AAC5B,QAAM;IAAC1W;IAAQmf;IAAgBpH;UAAkBlZ,0BAAW4gB,eAAD;AAC3D,QAAMe,yBAAyB9I,YAAYyH,cAAD;AAC1C,QAAMsB,mBAAmB/I,YAAY1X,UAAD,OAAA,SAACA,OAAQC,EAAT;AAGpCnB,+BAAU,MAAA;AACR,QAAI4X,UAAU;AACZ;;AAGF,QAAI,CAACyI,kBAAkBqB,0BAA0BC,oBAAoB,MAAM;AACzE,UAAI,CAACvP,gBAAgBsP,sBAAD,GAA0B;AAC5C;;AAGF,UAAIvV,SAASyV,kBAAkBF,uBAAuB/a,QAAQ;AAE5D;;AAGF,YAAMuS,gBAAgBD,eAAevT,IAAIic,gBAAnB;AAEtB,UAAI,CAACzI,eAAe;AAClB;;AAGF,YAAM;QAAChF;QAAelK;UAAQkP;AAE9B,UAAI,CAAChF,cAAc/B,WAAW,CAACnI,KAAKmI,SAAS;AAC3C;;AAGF0P,4BAAsB,MAAA;AACpB,mBAAWtY,WAAW,CAAC2K,cAAc/B,SAASnI,KAAKmI,OAA7B,GAAuC;AAC3D,cAAI,CAAC5I,SAAS;AACZ;;AAGF,gBAAMuY,gBAAgBC,uBAAuBxY,OAAD;AAE5C,cAAIuY,eAAe;AACjBA,0BAAcE,MAAd;AACA;;;OAVe;;KAetB,CACD3B,gBACAzI,UACAqB,gBACA0I,kBACAD,sBALC,CA1CM;AAkDT,SAAO;AACR;SClEeO,eACdC,WAAAA,MAAAA;MACA;IAACxa;IAAW,GAAGya;;AAEf,SAAOD,aAAS,QAATA,UAAWjd,SACdid,UAAUhc,OAAkB,CAACC,aAAa8B,aAAd;AAC1B,WAAOA,SAAS;MACdP,WAAWvB;MACX,GAAGgc;KAFU;KAIdza,SALH,IAMAA;AACL;SCVe0a,0BACdnI,QAAAA;AAEA,aAAO7X;IACL,OAAO;MACLrB,WAAW;QACT,GAAG4e,8BAA8B5e;QACjC,GAAGkZ,UAAH,OAAA,SAAGA,OAAQlZ;;MAEb6e,WAAW;QACT,GAAGD,8BAA8BC;QACjC,GAAG3F,UAAH,OAAA,SAAGA,OAAQ2F;;MAEbG,aAAa;QACX,GAAGJ,8BAA8BI;QACjC,GAAG9F,UAAH,OAAA,SAAGA,OAAQ8F;;;;IAIf,CAAC9F,UAAD,OAAA,SAACA,OAAQlZ,WAAWkZ,UAApB,OAAA,SAAoBA,OAAQ2F,WAAW3F,UAAvC,OAAA,SAAuCA,OAAQ8F,WAA/C;EAhBY;AAkBf;SCXesC,iCAAAA,MAAAA;MAAiC;IAC/CpQ;IACA9D;IACA+O;IACAjD,SAAS;;AAET,QAAMqI,kBAAcrK,sBAAO,KAAD;AAC1B,QAAM;IAAC1U;IAAGC;MAAK,OAAOyW,WAAW,YAAY;IAAC1W,GAAG0W;IAAQzW,GAAGyW;MAAUA;AAEtE2C,4BAA0B,MAAA;AACxB,UAAMhF,WAAW,CAACrU,KAAK,CAACC;AAExB,QAAIoU,YAAY,CAAC3F,YAAY;AAC3BqQ,kBAAYnQ,UAAU;AACtB;;AAGF,QAAImQ,YAAYnQ,WAAW,CAAC+K,aAAa;AAGvC;;AAIF,UAAMlT,OAAOiI,cAAH,OAAA,SAAGA,WAAYjI,KAAKmI;AAE9B,QAAI,CAACnI,QAAQA,KAAKsS,gBAAgB,OAAO;AAGvC;;AAGF,UAAMtY,OAAOmK,QAAQnE,IAAD;AACpB,UAAMuY,YAAYxa,aAAa/D,MAAMkZ,WAAP;AAE9B,QAAI,CAAC3Z,GAAG;AACNgf,gBAAUhf,IAAI;;AAGhB,QAAI,CAACC,GAAG;AACN+e,gBAAU/e,IAAI;;AAIhB8e,gBAAYnQ,UAAU;AAEtB,QAAIvO,KAAK+J,IAAI4U,UAAUhf,CAAnB,IAAwB,KAAKK,KAAK+J,IAAI4U,UAAU/e,CAAnB,IAAwB,GAAG;AAC1D,YAAM2H,0BAA0BD,2BAA2BlB,IAAD;AAE1D,UAAImB,yBAAyB;AAC3BA,gCAAwBmI,SAAS;UAC/BhP,KAAKie,UAAU/e;UACfY,MAAMme,UAAUhf;SAFlB;;;KAMH,CAAC0O,YAAY1O,GAAGC,GAAG0Z,aAAa/O,OAAhC,CA/CsB;AAgD1B;ACoDM,IAAMqU,6BAAyB7iB,6BAAyB;EAC7D,GAAGyD;EACHyE,QAAQ;EACRC,QAAQ;AAHqD,CAAZ;AAMnD,IAAK2a;CAAL,SAAKA,SAAAA;AACHA,EAAAA,QAAAA,QAAAA,eAAAA,IAAAA,CAAAA,IAAA;AACAA,EAAAA,QAAAA,QAAAA,cAAAA,IAAAA,CAAAA,IAAA;AACAA,EAAAA,QAAAA,QAAAA,aAAAA,IAAAA,CAAAA,IAAA;AACD,GAJIA,WAAAA,SAAM,CAAA,EAAX;AAMA,IAAaC,iBAAaC,oBAAK,SAASD,YAAT,MAAA;;MAAoB;IACjDvhB;IACAyhB;IACAvK,aAAa;IACb6G;IACAhc,UAAUuc;IACVoD,qBAAqBzb;IACrB0b;IACAZ;IACA,GAAG9Q;;AAEH,QAAM2R,YAAQC,0BAAWhC,SAAS9P,QAAW2P,eAArB;AACxB,QAAM,CAACI,OAAOvgB,QAAR,IAAoBqiB;AAC1B,QAAM,CAACE,sBAAsBC,uBAAvB,IACJ/iB,sBAAqB;AACvB,QAAM,CAACgjB,QAAQC,SAAT,QAAsB/iB,wBAAiBoiB,OAAOY,aAAhB;AACpC,QAAMC,gBAAgBH,WAAWV,OAAOc;AACxC,QAAM;IACJxiB,WAAW;MAACG,QAAQsiB;MAAU1C,OAAO7H;MAAgB8H;;IACrDnB,WAAW;MAAC9F,YAAYvU;;MACtB0b;AACJ,QAAMjX,OAAOwZ,YAAY,OAAOvK,eAAevT,IAAI8d,QAAnB,IAA+B;AAC/D,QAAMC,kBAAcxL,sBAAkC;IACpDyL,SAAS;IACTC,YAAY;GAFY;AAI1B,QAAMziB,aAASkB,uBACb,MAAA;AAAA,QAAA;AAAA,WACEohB,YAAY,OACR;MACEriB,IAAIqiB;;MAEJ/e,OAAI,aAAEuF,QAAF,OAAA,SAAEA,KAAMvF,SAAR,OAAA,aAAgBib;MACpB1b,MAAMyf;QAER;KACN,CAACD,UAAUxZ,IAAX,CAVoB;AAYtB,QAAM4Z,gBAAY3L,sBAAgC,IAA1B;AACxB,QAAM,CAAC4L,cAAcC,eAAf,QAAkCzjB,wBAAgC,IAAxB;AAChD,QAAM,CAACggB,gBAAgB0D,iBAAjB,QAAsC1jB,wBAAuB,IAAf;AACpD,QAAM2jB,cAAcvJ,eAAerJ,OAAO/N,OAAO6a,OAAO9M,KAAd,CAAR;AAClC,QAAM6S,yBAAyBhiB,YAAW,kBAAmBd,EAAnB;AAC1C,QAAM+iB,iCAA6B9hB,uBACjC,MAAMmD,oBAAoB2a,WAApB,GACN,CAAC3a,mBAAD,CAFwC;AAI1C,QAAMib,yBAAyB4B,0BAA0BU,SAAD;AACxD,QAAM;IAACxd;IAAgBoV;IAA4BK;MACjDlB,sBAAsBqK,4BAA4B;IAChDnK,UAAUuJ;IACVtJ,cAAc,CAAC+G,UAAUxd,GAAGwd,UAAUvd,CAAxB;IACdyW,QAAQuG,uBAAuBZ;GAHZ;AAKvB,QAAM3N,aAAa+G,cAAcC,gBAAgBuK,QAAjB;AAChC,QAAMW,4BAAwB/hB,uBAC5B,MAAOie,iBAAiBnc,oBAAoBmc,cAAD,IAAmB,MAC9D,CAACA,cAAD,CAFmC;AAIrC,QAAM+D,oBAAoBC,uBAAsB;AAChD,QAAMC,wBAAwBlJ,eAC5BnJ,YACAuO,uBAAuBzf,UAAUoN,OAFS;AAK5CkU,mCAAiC;IAC/BpQ,YAAYuR,YAAY,OAAOvK,eAAevT,IAAI8d,QAAnB,IAA+B;IAC9DvJ,QAAQmK,kBAAkBG;IAC1BrH,aAAaoH;IACbnW,SAASqS,uBAAuBzf,UAAUoN;GAJZ;AAOhC,QAAMmS,iBAAiBrE,QACrBhK,YACAuO,uBAAuBzf,UAAUoN,SACjCmW,qBAH4B;AAK9B,QAAM/D,oBAAoBtE,QACxBhK,aAAaA,WAAWuS,gBAAgB,IADT;AAGjC,QAAMC,oBAAgBxM,sBAAsB;IAC1CoI,gBAAgB;IAChBnf,QAAQ;IACR+Q;IACA5M,eAAe;IACfN,YAAY;IACZO;IACA2T;IACAyL,cAAc;IACdC,kBAAkB;IAClBpf;IACAlE,MAAM;IACN0M,qBAAqB,CAAA;IACrB6W,yBAAyB;GAbC;AAe5B,QAAMC,WAAWtf,oBAAoB4a,YAApB,wBACfsE,cAActS,QAAQ9Q,SADP,OAAA,SACf,sBAA4BF,EADb;AAGjB,QAAM4e,cAAcX,wBAAwB;IAC1CjR,SAASqS,uBAAuBT,YAAY5R;GADH;AAK3C,QAAMuW,gBAAY,wBAAG3E,YAAYT,QAAQnN,YAAvB,OAAA,wBAAkCF;AACpD,QAAM0S,mBAAmBrB,iBAAa,oBAClCvD,YAAY/b,SADsB,OAAA,oBACdsc,iBACpB;AACJ,QAAMwE,kBAAkBzQ,QACtB0L,YAAYT,QAAQnN,WAAW4N,YAAY/b,IADd;AAK/B,QAAM+gB,gBAAgB9H,aAAa6H,kBAAkB,OAAOxE,cAA1B;AAGlC,QAAMzB,aAAaH,cACjBgG,eAAejb,UAAUib,YAAD,IAAiB,IADX;AAKhC,QAAM3W,sBAAsBoP,uBAC1BmG,gBAAgBuB,YAAH,OAAGA,WAAY5S,aAAa,IADO;AAGlD,QAAMwF,0BAA0BkH,SAAS5Q,mBAAD;AAGxC,QAAMiX,oBAAoB/C,eAAeC,WAAW;IAClDxa,WAAW;MACTnE,GAAGwd,UAAUxd,IAAIwhB,cAAcxhB;MAC/BC,GAAGud,UAAUvd,IAAIuhB,cAAcvhB;MAC/BqE,QAAQ;MACRC,QAAQ;;IAEVuY;IACAnf;IACAof;IACAC;IACAoE;IACAtjB,MAAMojB,cAActS,QAAQ9Q;IAC5B4jB,iBAAiBlF,YAAY/b;IAC7B+J;IACA0J;IACAoH;GAhBsC;AAmBxC,QAAMrX,qBAAqB2c,wBACvB3jB,IAAI2jB,uBAAuBpD,SAAxB,IACH;AAEJ,QAAMtS,gBAAgB6O,iBAAiBvP,mBAAD;AAEtC,QAAMmX,mBAAmB/G,sBAAsB1P,aAAD;AAE9C,QAAM0W,wBAAwBhH,sBAAsB1P,eAAe,CACjE6R,cADiE,CAAhB;AAInD,QAAMsE,0BAA0BpkB,IAAIwkB,mBAAmBE,gBAApB;AAEnC,QAAM7f,gBAAgBsf,mBAClBrc,gBAAgBqc,kBAAkBK,iBAAnB,IACf;AAEJ,QAAMjgB,aACJ7D,UAAUmE,gBACNwd,mBAAmB;IACjB3hB;IACAmE;IACAC;IACAC,qBAAqB2e;IACrB1c;GALgB,IAOlB;AACN,QAAM4d,SAAStgB,kBAAkBC,YAAY,IAAb;AAChC,QAAM,CAAC1D,MAAMgkB,OAAP,QAAkBhlB,wBAAsB,IAAd;AAIhC,QAAMilB,mBAAmBR,kBACrBE,oBACAxkB,IAAIwkB,mBAAmBG,qBAApB;AAEP,QAAMzd,YAAYD,YAChB6d,mBAD2B,aAE3BjkB,QAF2B,OAAA,SAE3BA,KAAM2C,SAFqB,OAAA,aAEb,MACdsc,cAH2B;AAM7B,QAAMiF,sBAAkBtN,sBAA8B,IAAxB;AAC9B,QAAMuN,wBAAoBjlB;IACxB,CACEK,OADF,UAAA;UAEE;QAACmC,QAAQuW;QAAQtW;;AAEjB,UAAI4gB,UAAUzR,WAAW,MAAM;AAC7B;;AAGF,YAAMF,cAAagH,eAAevT,IAAIke,UAAUzR,OAA7B;AAEnB,UAAI,CAACF,aAAY;AACf;;AAGF,YAAMoO,kBAAiBzf,MAAMoT;AAE7B,YAAMyR,iBAAiB,IAAInM,OAAO;QAChCpY,QAAQ0iB,UAAUzR;QAClBF,YAAAA;QACArR,OAAOyf;QACPrd;;;QAGAqP,SAASoS;QACTvO,QAAQ/U,KAAD;AACL,gBAAM+X,gBAAgBD,eAAevT,IAAIvE,GAAnB;AAEtB,cAAI,CAAC+X,eAAe;AAClB;;AAGF,gBAAM;YAACwM;cAAe1B,YAAY7R;AAClC,gBAAMvR,SAAwB;YAACO,IAAAA;;AAC/BukB,yBAAW,OAAX,SAAAA,YAAc9kB,MAAH;AACXqiB,+BAAqB;YAACtiB,MAAM;YAAeC,OAAAA;WAAvB;;QAEtB+U,UAAUxU,KAAIiT,YAAYO,oBAAoBe,QAArC;AACP,gBAAMwD,gBAAgBD,eAAevT,IAAIvE,GAAnB;AAEtB,cAAI,CAAC+X,eAAe;AAClB;;AAGF,gBAAM;YAACyM;cAAiB3B,YAAY7R;AACpC,gBAAMvR,SAA0B;YAC9BO,IAAAA;YACAiT;YACAO;YACAe;;AAGFiQ,2BAAa,OAAb,SAAAA,cAAgB/kB,MAAH;AACbqiB,+BAAqB;YAACtiB,MAAM;YAAiBC,OAAAA;WAAzB;;QAEtBsR,QAAQyC,oBAAD;AACL,gBAAMxT,MAAKyiB,UAAUzR;AAErB,cAAIhR,OAAM,MAAM;AACd;;AAGF,gBAAM+X,gBAAgBD,eAAevT,IAAIvE,GAAnB;AAEtB,cAAI,CAAC+X,eAAe;AAClB;;AAGF,gBAAM;YAACjY;cAAe+iB,YAAY7R;AAClC,gBAAMvR,SAAwB;YAC5Byf,gBAAAA;YACAnf,QAAQ;cAACC,IAAAA;cAAIsD,MAAMyU,cAAczU;cAAMT,MAAMyf;;;AAG/CmC,wDAAwB,MAAA;AACtB3kB,2BAAW,OAAX,SAAAA,YAAcL,MAAH;AACXwiB,sBAAUX,OAAOoD,YAAR;AACTnlB,qBAAS;cACPC,MAAMiC,OAAOyS;cACbV;cACAzT,QAAQC;aAHF;AAKR8hB,iCAAqB;cAACtiB,MAAM;cAAeC,OAAAA;aAAvB;AACpBkjB,4BAAgByB,gBAAgBpT,OAAjB;AACf4R,8BAAkB1D,eAAD;WAVI;;QAazB3M,OAAOD,aAAD;AACJ/S,mBAAS;YACPC,MAAMiC,OAAOue;YACb1N;WAFM;;QAKVE,OAAOmS,cAAcljB,OAAOwe,OAAR;QACpBvN,UAAUiS,cAAcljB,OAAOye,UAAR;OA7EF;AAgFvBkE,sBAAgBpT,UAAUsT;AAE1B,eAASK,cAAcnlB,MAAvB;AACE,eAAO,eAAe2O,UAAf;AACL,gBAAM;YAACpO,QAAAA;YAAQ6D,YAAAA;YAAY1D,MAAAA;YAAMujB,yBAAAA;cAC/BH,cAActS;AAChB,cAAIvR,SAA6B;AAEjC,cAAIM,WAAU0jB,0BAAyB;AACrC,kBAAM;cAACmB;gBAAc/B,YAAY7R;AAEjCvR,YAAAA,SAAQ;cACNyf,gBAAAA;cACAnf,QAAQA;cACR6D,YAAAA;cACA4K,OAAOiV;cACPvjB,MAAAA;;AAGF,gBAAIV,SAASiC,OAAOwe,WAAW,OAAO2E,eAAe,YAAY;AAC/D,oBAAMC,eAAe,MAAMC,QAAQC,QAAQH,WAAWnlB,MAAD,CAA1B;AAE3B,kBAAIolB,cAAc;AAChBrlB,uBAAOiC,OAAOye;;;;AAKpBuC,oBAAUzR,UAAU;AAEpByT,wDAAwB,MAAA;AACtBllB,qBAAS;cAACC;aAAF;AACRyiB,sBAAUX,OAAOY,aAAR;AACTgC,oBAAQ,IAAD;AACPvB,4BAAgB,IAAD;AACfC,8BAAkB,IAAD;AACjBwB,4BAAgBpT,UAAU;AAE1B,kBAAM9C,YACJ1O,SAASiC,OAAOwe,UAAU,cAAc;AAE1C,gBAAIxgB,QAAO;AACT,oBAAM0O,WAAU0U,YAAY7R,QAAQ9C,SAApB;AAEhBC,cAAAA,YAAO,OAAP,SAAAA,SAAU1O,MAAH;AACPqiB,mCAAqB;gBAACtiB,MAAM0O;gBAAWzO,OAAAA;eAAnB;;WAfD;;;;;IAsB7B,CAACqY,cAAD;EArJmC;AAwJrC,QAAMkN,wCAAoC5lB,2BACxC,CACE+O,SACAvM,WAFF;AAIE,WAAO,CAACnC,OAAOM,YAAR;AACL,YAAM8S,cAAcpT,MAAMoT;AAC1B,YAAMoS,sBAAsBnN,eAAevT,IAAIxE,OAAnB;AAE5B;;QAEE0iB,UAAUzR,YAAY;QAEtB,CAACiU;QAEDpS,YAAYqS,UACZrS,YAAYsS;QACZ;AACA;;AAGF,YAAMC,oBAAoB;QACxBrlB,QAAQklB;;AAEV,YAAMI,iBAAiBlX,QACrB1O,OACAmC,OAAOC,SACPujB,iBAH4B;AAM9B,UAAIC,mBAAmB,MAAM;AAC3BxS,oBAAYqS,SAAS;UACnBI,YAAY1jB,OAAOA;;AAGrB6gB,kBAAUzR,UAAUjR;AACpBskB,0BAAkB5kB,OAAOmC,MAAR;;;KAIvB,CAACkW,gBAAgBuM,iBAAjB,CAxCmD;AA2CrD,QAAM1R,aAAasF,qBACjBlW,SACAijB,iCAFqC;AAKvC5H,iBAAerb,OAAD;AAEd0Z,4BAA0B,MAAA;AACxB,QAAI0D,kBAAkB6C,WAAWV,OAAOoD,cAAc;AACpDzC,gBAAUX,OAAOc,WAAR;;KAEV,CAACjD,gBAAgB6C,MAAjB,CAJsB;AAMzBnjB;IACE,MAAA;AACE,YAAM;QAACqC;UAAc2hB,YAAY7R;AACjC,YAAM;QAACjR,QAAAA;QAAQmf,gBAAAA;QAAgBtb,YAAAA;QAAY1D,MAAAA;UAAQojB,cAActS;AAEjE,UAAI,CAACjR,WAAU,CAACmf,iBAAgB;AAC9B;;AAGF,YAAMzf,QAAuB;QAC3BM,QAAAA;QACAmf,gBAAAA;QACAtb,YAAAA;QACA4K,OAAO;UACLpM,GAAGqhB,wBAAwBrhB;UAC3BC,GAAGohB,wBAAwBphB;;QAE7BnC,MAAAA;;AAGFukB,oDAAwB,MAAA;AACtBvjB,sBAAU,OAAV,SAAAA,WAAazB,KAAH;AACVqiB,6BAAqB;UAACtiB,MAAM;UAAcC;SAAtB;OAFC;;;IAMzB,CAACgkB,wBAAwBrhB,GAAGqhB,wBAAwBphB,CAApD;EA1BO;AA6BTxD;IACE,MAAA;AACE,YAAM;QACJkB,QAAAA;QACAmf,gBAAAA;QACAtb,YAAAA;QACAQ,qBAAAA;QACAqf,yBAAAA;UACEH,cAActS;AAElB,UACE,CAACjR,WACD0iB,UAAUzR,WAAW,QACrB,CAACkO,mBACD,CAACuE,0BACD;AACA;;AAGF,YAAM;QAACxjB;UAAc4iB,YAAY7R;AACjC,YAAMuU,gBAAgBnhB,qBAAoBG,IAAI0f,MAAxB;AACtB,YAAM/jB,QACJqlB,iBAAiBA,cAAc1iB,KAAKmO,UAChC;QACEhR,IAAIulB,cAAcvlB;QAClB6C,MAAM0iB,cAAc1iB,KAAKmO;QACzB1N,MAAMiiB,cAAcjiB;QACpBmT,UAAU8O,cAAc9O;UAE1B;AACN,YAAMhX,QAAuB;QAC3BM,QAAAA;QACAmf,gBAAAA;QACAtb,YAAAA;QACA4K,OAAO;UACLpM,GAAGqhB,yBAAwBrhB;UAC3BC,GAAGohB,yBAAwBphB;;QAE7BnC,MAAAA;;AAGFukB,oDAAwB,MAAA;AACtBP,gBAAQhkB,KAAD;AACPD,sBAAU,OAAV,SAAAA,WAAaR,KAAH;AACVqiB,6BAAqB;UAACtiB,MAAM;UAAcC;SAAtB;OAHC;;;IAOzB,CAACwkB,MAAD;EAhDO;AAmDTxI,4BAA0B,MAAA;AACxB6H,kBAActS,UAAU;MACtBkO;MACAnf;MACA+Q;MACA5M;MACAN;MACAO;MACA2T;MACAyL;MACAC;MACApf;MACAlE;MACA0M;MACA6W;;AAGFnB,gBAAYtR,UAAU;MACpBuR,SAASiB;MACThB,YAAYte;;KAEb,CACDnE,QACA+Q,YACAlN,YACAM,eACA4T,gBACAyL,cACAC,kBACArf,gBACAC,qBACAlE,MACA0M,qBACA6W,uBAZC,CArBsB;AAoCzB3N,kBAAgB;IACd,GAAGmN;IACHzU,OAAOoR;IACP3J,cAAc/R;IACdmC;IACAuG;IACA0J;GANa;AASf,QAAMkP,oBAAgBvkB,uBAAQ,MAAA;AAC5B,UAAMiQ,UAAmC;MACvCnR;MACA+Q;MACAqO;MACAD;MACAtb;MACAwb;MACAR;MACA9G;MACA1T;MACAD;MACAjE;MACAqZ;MACA3M;MACA0J;MACA+I;MACAzF;MACA8D;;AAGF,WAAOxM;KACN,CACDnR,QACA+Q,YACAqO,gBACAD,gBACAtb,YACAwb,mBACAR,aACA9G,gBACA1T,qBACAD,gBACAjE,MACAqZ,4BACA3M,qBACA0J,yBACA+I,wBACAzF,oBACA8D,UAjBC,CAtB0B;AA0C7B,QAAM+H,sBAAkBxkB,uBAAQ,MAAA;AAC9B,UAAMiQ,UAAqC;MACzCgO;MACAvM;MACA5S;MACAof;MACAI,mBAAmB;QACjB3f,WAAWkjB;;MAEbvjB;MACAuY;MACA5X;MACAqZ;;AAGF,WAAOrI;KACN,CACDgO,gBACAvM,YACA5S,QACAof,gBACA5f,UACAujB,wBACAhL,gBACA5X,MACAqZ,0BATC,CAhB4B;AA4B/B,SACEnY,cAAAA,QAAAA,cAAC7C,kBAAkBmnB,UAAnB;IAA4BpkB,OAAOygB;KACjC3gB,cAAAA,QAAAA,cAACoe,gBAAgBkG,UAAjB;IAA0BpkB,OAAOmkB;KAC/BrkB,cAAAA,QAAAA,cAACqe,cAAciG,UAAf;IAAwBpkB,OAAOkkB;KAC7BpkB,cAAAA,QAAAA,cAACigB,uBAAuBqE,UAAxB;IAAiCpkB,OAAOiF;KACrCwX,QADH,CADF,GAKA3c,cAAAA,QAAAA,cAACkf,cAAD;IAAc7J,WAAUgL,iBAAa,OAAb,SAAAA,cAAekE,kBAAiB;GAAxD,CANF,GAQAvkB,cAAAA,QAAAA,cAACf,eAAD;IAAA,GACMohB;IACJjhB,yBAAyBsiB;GAF3B,CATF;AAgBF,WAASI,yBAAT;AACE,UAAM0C,kCACJlD,gBAAY,OAAZ,SAAAA,aAAcxS,uBAAsB;AACtC,UAAM2V,6BACJ,OAAO3O,eAAe,WAClBA,WAAWhB,YAAY,QACvBgB,eAAe;AACrB,UAAMhB,UACJiM,iBACA,CAACyD,kCACD,CAACC;AAEH,QAAI,OAAO3O,eAAe,UAAU;AAClC,aAAO;QACL,GAAGA;QACHhB;;;AAIJ,WAAO;MAACA;;;AAEX,CAtnB6B;ACrG9B,IAAM4P,kBAActnB,6BAAmB,IAAN;AAEjC,IAAMunB,cAAc;AAEpB,IAAMC,YAAY;AAElB,SAAgBC,aAAAA,MAAAA;MAAa;IAC3BjmB;IACAsD;IACAmT,WAAW;IACXyP;;AAEA,QAAMxY,OAAM5M,YAAYklB,SAAD;AACvB,QAAM;IACJrT;IACAuM;IACAnf;IACAof;IACAI;IACAzH;IACA5X;UACEtB,0BAAW4gB,eAAD;AACd,QAAM;IACJ2G,OAAOJ;IACPK,kBAAkB;IAClBC,WAAW;MACTH,cAJE,OAIFA,aAAc,CAAA;AAClB,QAAMI,cAAavmB,UAAM,OAAN,SAAAA,OAAQC,QAAOA;AAClC,QAAMuG,gBAA8B3H,0BAClC0nB,aAAajF,yBAAyByE,WADM;AAG9C,QAAM,CAACjd,MAAM0d,UAAP,IAAqBlI,WAAU;AACrC,QAAM,CAACtL,eAAeyT,mBAAhB,IAAuCnI,WAAU;AACvD,QAAMpf,YAAYqe,sBAAsB3K,YAAY3S,EAAb;AACvC,QAAMymB,UAAUnN,eAAehW,IAAD;AAE9BmY;IACE,MAAA;AACE3D,qBAAe6B,IAAI3Z,IAAI;QAACA;QAAI0N,KAAAA;QAAK7E;QAAMkK;QAAezP,MAAMmjB;OAA5D;AAEA,aAAO,MAAA;AACL,cAAM5d,QAAOiP,eAAevT,IAAIvE,EAAnB;AAEb,YAAI6I,SAAQA,MAAK6E,QAAQA,MAAK;AAC5BoK,yBAAexY,OAAOU,EAAtB;;;;;IAKN,CAAC8X,gBAAgB9X,EAAjB;EAbuB;AAgBzB,QAAM0mB,yBAA0CzlB,uBAC9C,OAAO;IACLklB;IACAE;IACA,iBAAiB5P;IACjB,gBAAgB6P,cAAcH,SAASJ,cAAc,OAAOhW;IAC5D,wBAAwBqW;IACxB,oBAAoB7G,kBAAkB3f;MAExC,CACE6W,UACA0P,MACAE,UACAC,YACAF,iBACA7G,kBAAkB3f,SANpB,CATqD;AAmBvD,SAAO;IACLG;IACAmf;IACAC;IACA+G,YAAYQ;IACZJ;IACArnB,WAAWwX,WAAW1G,SAAY9Q;IAClC4J;IACA3I;IACAqmB;IACAC;IACAjgB;;AAEH;SCrHeogB,gBAAAA;AACd,aAAO/nB,0BAAW6gB,aAAD;AAClB;ACsBD,IAAMuG,cAAY;AAElB,IAAMY,8BAA8B;EAClCC,SAAS;AADyB;AAIpC,SAAgBC,aAAAA,MAAAA;MAAa;IAC3BxjB;IACAmT,WAAW;IACXzW;IACA+mB;;AAEA,QAAMrZ,OAAM5M,YAAYklB,WAAD;AACvB,QAAM;IAACjmB;IAAQR;IAAUW;IAAMqZ;UAC7B3a,0BAAW4gB,eAAD;AACZ,QAAMwH,eAAWlQ,sBAAO;IAACL;GAAF;AACvB,QAAMwQ,8BAA0BnQ,sBAAO,KAAD;AACtC,QAAMjU,WAAOiU,sBAA0B,IAApB;AACnB,QAAMoQ,iBAAapQ,sBAA8B,IAAxB;AACzB,QAAM;IACJL,UAAU0Q;IACVC;IACAP,SAASQ;MACP;IACF,GAAGT;IACH,GAAGG;;AAEL,QAAMvN,MAAMF,eAAe8N,yBAAD,OAACA,wBAAyBpnB,EAA1B;AAC1B,QAAM0a,mBAAetb;IACnB,MAAA;AACE,UAAI,CAAC6nB,wBAAwBjW,SAAS;AAGpCiW,gCAAwBjW,UAAU;AAClC;;AAGF,UAAIkW,WAAWlW,WAAW,MAAM;AAC9BsD,qBAAa4S,WAAWlW,OAAZ;;AAGdkW,iBAAWlW,UAAUJ,WAAW,MAAA;AAC9B2I,mCACEsD,MAAMyK,QAAQ9N,IAAIxI,OAAlB,IAA6BwI,IAAIxI,UAAU,CAACwI,IAAIxI,OAAL,CADnB;AAG1BkW,mBAAWlW,UAAU;SACpBqW,qBAL4B;;;IAQjC,CAACA,qBAAD;EArB8B;AAuBhC,QAAM1M,iBAAiBF,kBAAkB;IACvCN,UAAUO;IACVjE,UAAU0Q,0BAA0B,CAACpnB;GAFC;AAIxC,QAAMme,uBAAmB9e,2BACvB,CAACmoB,YAAgCC,oBAAjC;AACE,QAAI,CAAC7M,gBAAgB;AACnB;;AAGF,QAAI6M,iBAAiB;AACnB7M,qBAAe8M,UAAUD,eAAzB;AACAP,8BAAwBjW,UAAU;;AAGpC,QAAIuW,YAAY;AACd5M,qBAAee,QAAQ6L,UAAvB;;KAGJ,CAAC5M,cAAD,CAfkC;AAiBpC,QAAM,CAACwD,SAASoI,UAAV,IAAwBlI,WAAWH,gBAAD;AACxC,QAAMuI,UAAUnN,eAAehW,IAAD;AAE9BzE,+BAAU,MAAA;AACR,QAAI,CAAC8b,kBAAkB,CAACwD,QAAQnN,SAAS;AACvC;;AAGF2J,mBAAeH,WAAf;AACAyM,4BAAwBjW,UAAU;AAClC2J,mBAAee,QAAQyC,QAAQnN,OAA/B;KACC,CAACmN,SAASxD,cAAV,CARM;AAUT9b;IACE,MAAA;AACEU,eAAS;QACPC,MAAMiC,OAAO0e;QACb/X,SAAS;UACPpI;UACA0N,KAAAA;UACA+I;UACA5N,MAAMsV;UACNtb;UACAS,MAAMmjB;;OARF;AAYR,aAAO,MACLlnB,SAAS;QACPC,MAAMiC,OAAO4e;QACb3S,KAAAA;QACA1N;OAHM;;;IAOZ,CAACA,EAAD;EAtBO;AAyBTnB,+BAAU,MAAA;AACR,QAAI4X,aAAauQ,SAAShW,QAAQyF,UAAU;AAC1ClX,eAAS;QACPC,MAAMiC,OAAO2e;QACbpgB;QACA0N,KAAAA;QACA+I;OAJM;AAORuQ,eAAShW,QAAQyF,WAAWA;;KAE7B,CAACzW,IAAI0N,MAAK+I,UAAUlX,QAApB,CAXM;AAaT,SAAO;IACLQ;IACA8C;IACA6kB,SAAQxnB,QAAI,OAAJ,SAAAA,KAAMF,QAAOA;IACrB6I,MAAMsV;IACNje;IACAqmB;;AAEH;SC/IeoB,iBAAAA,MAAAA;MAAiB;IAACC;IAAW7J;;AAC3C,QAAM,CACJ8J,gBACAC,iBAFI,QAGF5oB,wBAAoC,IAA5B;AACZ,QAAM,CAACkJ,SAAS2f,UAAV,QAAwB7oB,wBAA6B,IAArB;AACtC,QAAM8oB,mBAAmBvQ,YAAYsG,QAAD;AAEpC,MAAI,CAACA,YAAY,CAAC8J,kBAAkBG,kBAAkB;AACpDF,sBAAkBE,gBAAD;;AAGnBvM,4BAA0B,MAAA;AACxB,QAAI,CAACrT,SAAS;AACZ;;AAGF,UAAMsF,OAAMma,kBAAH,OAAA,SAAGA,eAAgBna;AAC5B,UAAM1N,KAAK6nB,kBAAH,OAAA,SAAGA,eAAgB5X,MAAMjQ;AAEjC,QAAI0N,QAAO,QAAQ1N,MAAM,MAAM;AAC7B8nB,wBAAkB,IAAD;AACjB;;AAGFhD,YAAQC,QAAQ6C,UAAU5nB,IAAIoI,OAAL,CAAzB,EAAwC6f,KAAK,MAAA;AAC3CH,wBAAkB,IAAD;KADnB;KAGC,CAACF,WAAWC,gBAAgBzf,OAA5B,CAhBsB;AAkBzB,SACEhH,cAAAA,QAAAA,cAAA,cAAAA,QAAA,UAAA,MACG2c,UACA8J,qBAAiBK,4BAAaL,gBAAgB;IAACM,KAAKJ;GAAvB,IAAsC,IAFtE;AAKH;ACzCD,IAAMK,mBAA8B;EAClChmB,GAAG;EACHC,GAAG;EACHqE,QAAQ;EACRC,QAAQ;AAJ0B;AAOpC,SAAgB0hB,yBAAAA,MAAAA;MAAyB;IAACtK;;AACxC,SACE3c,cAAAA,QAAAA,cAACoe,gBAAgBkG,UAAjB;IAA0BpkB,OAAOge;KAC/Ble,cAAAA,QAAAA,cAACigB,uBAAuBqE,UAAxB;IAAiCpkB,OAAO8mB;KACrCrK,QADH,CADF;AAMH;ACAD,IAAMuK,aAAkC;EACtCvf,UAAU;EACVwf,aAAa;AAFyB;AAKxC,IAAMC,oBAAuCtJ,oBAAD;AAC1C,QAAMuJ,sBAAsBxX,gBAAgBiO,cAAD;AAE3C,SAAOuJ,sBAAsB,yBAAyB1Y;AACvD;AAEM,IAAM2Y,wBAAoBC,0BAC/B,CAAA,MAYER,QAZF;MACE;IACES;IACA1J;IACA5Y,aAAAA;IACAyX;IACA8K;IACAhmB;IACAimB;IACAviB;IACAwiB,aAAaP;;AAIf,MAAI,CAAC3lB,MAAM;AACT,WAAO;;AAGT,QAAMmmB,yBAAyB1iB,eAC3BC,YACA;IACE,GAAGA;IACHG,QAAQ;IACRC,QAAQ;;AAEd,QAAMsiB,SAA0C;IAC9C,GAAGX;IACHplB,OAAOL,KAAKK;IACZE,QAAQP,KAAKO;IACbD,KAAKN,KAAKM;IACVF,MAAMJ,KAAKI;IACXsD,WAAW2iB,IAAIC,UAAUC,SAASJ,sBAAvB;IACXhmB,iBACEsD,gBAAe4Y,iBACXtc,2BACEsc,gBACArc,IAFwB,IAI1BkN;IACNgZ,YACE,OAAOA,eAAe,aAClBA,WAAW7J,cAAD,IACV6J;IACN,GAAGD;;AAGL,SAAO1nB,cAAAA,QAAMioB,cACXT,IACA;IACEC;IACAC,OAAOG;IACPd;KAEFpK,QAPK;AASR,CAxDwC;ICwD9BuL,kCACXznB,aAC6B,UAAA;MAAC;IAAC9B;IAAQ6e;;AACvC,QAAM2K,iBAAyC,CAAA;AAC/C,QAAM;IAACN;IAAQJ;MAAahnB;AAE5B,MAAIonB,UAAJ,QAAIA,OAAQlpB,QAAQ;AAClB,eAAW,CAAC2N,MAAKpM,KAAN,KAAgBY,OAAOya,QAAQsM,OAAOlpB,MAAtB,GAA+B;AACxD,UAAIuB,UAAUyO,QAAW;AACvB;;AAGFwZ,qBAAe7b,IAAD,IAAQ3N,OAAO8I,KAAKigB,MAAMU,iBAAiB9b,IAAnC;AACtB3N,aAAO8I,KAAKigB,MAAMW,YAAY/b,MAAKpM,KAAnC;;;AAIJ,MAAI2nB,UAAJ,QAAIA,OAAQrK,aAAa;AACvB,eAAW,CAAClR,MAAKpM,KAAN,KAAgBY,OAAOya,QAAQsM,OAAOrK,WAAtB,GAAoC;AAC7D,UAAItd,UAAUyO,QAAW;AACvB;;AAGF6O,kBAAY/V,KAAKigB,MAAMW,YAAY/b,MAAKpM,KAAxC;;;AAIJ,MAAIunB,aAAJ,QAAIA,UAAW9oB,QAAQ;AACrBA,WAAO8I,KAAK6gB,UAAUrqB,IAAIwpB,UAAU9oB,MAApC;;AAGF,MAAI8oB,aAAJ,QAAIA,UAAWjK,aAAa;AAC1BA,gBAAY/V,KAAK6gB,UAAUrqB,IAAIwpB,UAAUjK,WAAzC;;AAGF,SAAO,SAASlC,UAAT;AACL,eAAW,CAAChP,MAAKpM,KAAN,KAAgBY,OAAOya,QAAQ4M,cAAf,GAAgC;AACzDxpB,aAAO8I,KAAKigB,MAAMW,YAAY/b,MAAKpM,KAAnC;;AAGF,QAAIunB,aAAJ,QAAIA,UAAW9oB,QAAQ;AACrBA,aAAO8I,KAAK6gB,UAAUC,OAAOd,UAAU9oB,MAAvC;;;AAGL;AAED,IAAM6pB,0BAA4C,WAAA;AAAA,MAAC;IACjDrjB,WAAW;MAACgc;MAASsH;;MAD2B;AAAA,SAE5C,CACJ;IACEtjB,WAAW2iB,IAAIC,UAAUC,SAAS7G,OAAvB;KAEb;IACEhc,WAAW2iB,IAAIC,UAAUC,SAASS,KAAvB;GALT;AAF4C;AAWlD,IAAaC,oCAAoE;EAC/EC,UAAU;EACVC,QAAQ;EACRC,WAAWL;EACXM,aAAaZ,gCAAgC;IAC3CL,QAAQ;MACNlpB,QAAQ;QACNoqB,SAAS;;;GAH6B;AAJmC;AAajF,SAAgBC,iBAAAA,OAAAA;MAAiB;IAC/BtR;IACAhB;IACA1T;IACAib;;AAEA,SAAOhF,SAAoB,CAACra,IAAI6I,SAAL;AACzB,QAAIiQ,WAAW,MAAM;AACnB;;AAGF,UAAMuR,kBAA6CvS,eAAevT,IAAIvE,EAAnB;AAEnD,QAAI,CAACqqB,iBAAiB;AACpB;;AAGF,UAAMvZ,aAAauZ,gBAAgBxhB,KAAKmI;AAExC,QAAI,CAACF,YAAY;AACf;;AAGF,UAAMwZ,iBAAiBxM,kBAAkBjV,IAAD;AAExC,QAAI,CAACyhB,gBAAgB;AACnB;;AAEF,UAAM;MAAC/jB;QAAa+B,UAAUO,IAAD,EAAON,iBAAiBM,IAAjC;AACpB,UAAMnB,kBAAkBN,eAAeb,SAAD;AAEtC,QAAI,CAACmB,iBAAiB;AACpB;;AAGF,UAAMkgB,YACJ,OAAO9O,WAAW,aACdA,SACAyR,2BAA2BzR,MAAD;AAEhC/L,2BACE+D,YACAuO,uBAAuBzf,UAAUoN,OAFb;AAKtB,WAAO4a,UAAU;MACf7nB,QAAQ;QACNC;QACAsD,MAAM+mB,gBAAgB/mB;QACtBuF,MAAMiI;QACNjO,MAAMwc,uBAAuBzf,UAAUoN,QAAQ8D,UAAzC;;MAERgH;MACA8G,aAAa;QACX/V;QACAhG,MAAMwc,uBAAuBT,YAAY5R,QAAQsd,cAA3C;;MAERlmB;MACAib;MACA9Y,WAAWmB;KAdG;GAvCH;AAwDhB;AAED,SAAS6iB,2BACP1oB,SADF;AAGE,QAAM;IAACkoB;IAAUC;IAAQE;IAAaD;MAAa;IACjD,GAAGH;IACH,GAAGjoB;;AAGL,SAAO,WAAA;QAAC;MAAC9B;MAAQ6e;MAAarY;MAAW,GAAGikB;;AAC1C,QAAI,CAACT,UAAU;AAEb;;AAGF,UAAMvb,QAAQ;MACZpM,GAAGwc,YAAY/b,KAAKI,OAAOlD,OAAO8C,KAAKI;MACvCZ,GAAGuc,YAAY/b,KAAKM,MAAMpD,OAAO8C,KAAKM;;AAGxC,UAAMsnB,QAAQ;MACZ/jB,QACEH,UAAUG,WAAW,IAChB3G,OAAO8C,KAAKK,QAAQqD,UAAUG,SAAUkY,YAAY/b,KAAKK,QAC1D;MACNyD,QACEJ,UAAUI,WAAW,IAChB5G,OAAO8C,KAAKO,SAASmD,UAAUI,SAAUiY,YAAY/b,KAAKO,SAC3D;;AAER,UAAMsnB,iBAAiB;MACrBtoB,GAAGmE,UAAUnE,IAAIoM,MAAMpM;MACvBC,GAAGkE,UAAUlE,IAAImM,MAAMnM;MACvB,GAAGooB;;AAGL,UAAME,qBAAqBV,UAAU;MACnC,GAAGO;MACHzqB;MACA6e;MACArY,WAAW;QAACgc,SAAShc;QAAWsjB,OAAOa;;KAJL;AAOpC,UAAM,CAACE,aAAD,IAAkBD;AACxB,UAAME,eAAeF,mBAAmBA,mBAAmB7mB,SAAS,CAA7B;AAEvC,QAAIuT,KAAKC,UAAUsT,aAAf,MAAkCvT,KAAKC,UAAUuT,YAAf,GAA8B;AAElE;;AAGF,UAAMnO,UAAUwN,eAAH,OAAA,SAAGA,YAAc;MAACnqB;MAAQ6e;MAAa,GAAG4L;KAA5B;AAC3B,UAAM5C,YAAYhJ,YAAY/V,KAAKiiB,QAAQH,oBAAoB;MAC7DZ;MACAC;MACAe,MAAM;KAHU;AAMlB,WAAO,IAAIjG,QAASC,aAAD;AACjB6C,gBAAUoD,WAAW,MAAA;AACnBtO,mBAAO,OAAP,SAAAA,QAAO;AACPqI,gBAAO;;KAHJ;;AAOV;AC9RD,IAAIrX,MAAM;AAEV,SAAgBud,OAAOjrB,IAAAA;AACrB,aAAOiB,uBAAQ,MAAA;AACb,QAAIjB,MAAM,MAAM;AACd;;AAGF0N;AACA,WAAOA;KACN,CAAC1N,EAAD,CAPW;AAQf;ICaYkrB,cAAc9pB,cAAAA,QAAMogB,KAC/B,UAAA;MAAC;IACClb,aAAAA,eAAc;IACdyX;IACAoN,eAAeC;IACftC;IACAC;IACAhI;IACAsK,iBAAiB;IACjBxC;IACAyC,SAAS;;AAET,QAAM;IACJpM;IACAnf;IACAof;IACAC;IACAtH;IACA1T;IACAwa;IACA1e;IACAmf;IACAzS;IACA0J;IACAoH;MACEiJ,cAAa;AACjB,QAAMpgB,gBAAY3H,0BAAWyiB,sBAAD;AAC5B,QAAM3T,OAAMud,OAAOlrB,UAAD,OAAA,SAACA,OAAQC,EAAT;AAClB,QAAMurB,oBAAoBzK,eAAeC,WAAW;IAClD7B;IACAnf;IACAof;IACAC;IACAoE,kBAAkB5E,YAAY/b;IAC9B3C;IACA4jB,iBAAiBlF,YAAY/b;IAC7B+J;IACA0J;IACA/P;IACAmX;GAXsC;AAaxC,QAAM3B,cAAchC,gBAAgBoF,cAAD;AACnC,QAAMgM,gBAAgBf,iBAAiB;IACrCtR,QAAQsS;IACRtT;IACA1T;IACAib;GAJoC;AAQtC,QAAM8I,MAAMpM,cAAc6C,YAAYR,SAASrO;AAE/C,SACE3O,cAAAA,QAAAA,cAACinB,0BAAD,MACEjnB,cAAAA,QAAAA,cAACumB,kBAAD;IAAkBC,WAAWuD;KAC1BprB,UAAU2N,OACTtM,cAAAA,QAAAA,cAACsnB,mBAAD;IACEhb,KAAKA;IACL1N,IAAID,OAAOC;IACXmoB;IACAS,IAAIyC;IACJnM;IACA5Y,aAAaA;IACbuiB;IACAE;IACAlmB,MAAMkZ;IACN+M,OAAO;MACLwC;MACA,GAAGxC;;IAELviB,WAAWglB;KAEVxN,QAhBH,IAkBE,IApBN,CADF;AAyBH,CA9EwB;", "names": ["hiddenStyles", "display", "HiddenText", "id", "value", "React", "style", "LiveRegion", "announcement", "ariaLiveType", "visuallyHidden", "position", "top", "left", "width", "height", "margin", "border", "padding", "overflow", "clip", "clipPath", "whiteSpace", "role", "useAnnouncement", "setAnnouncement", "useState", "announce", "useCallback", "DndMonitorContext", "createContext", "useDndMonitor", "listener", "registerListener", "useContext", "useEffect", "Error", "unsubscribe", "useDndMonitorProvider", "listeners", "useState", "Set", "useCallback", "add", "delete", "dispatch", "type", "event", "for<PERSON>ach", "defaultScreenReaderInstructions", "draggable", "defaultAnnouncements", "onDragStart", "active", "id", "onDragOver", "over", "onDragEnd", "onDragCancel", "Accessibility", "announcements", "container", "hiddenTextDescribedById", "screenReaderInstructions", "announce", "announcement", "useAnnouncement", "liveRegionId", "useUniqueId", "mounted", "setMounted", "useMemo", "onDragMove", "markup", "React", "HiddenText", "value", "LiveRegion", "createPortal", "Action", "noop", "useSensor", "sensor", "options", "useSensors", "sensors", "filter", "defaultCoordinates", "Object", "freeze", "x", "y", "distanceBetween", "p1", "p2", "Math", "sqrt", "pow", "getRelativeTransformOrigin", "rect", "eventCoordinates", "getEventCoordinates", "transform<PERSON><PERSON>in", "left", "width", "top", "height", "sortCollisionsAsc", "data", "a", "b", "sortCollisionsDesc", "cornersOfRectangle", "getFirstCollision", "collisions", "property", "length", "firstCollision", "centerOfRectangle", "closestCenter", "collisionRect", "droppableRects", "droppableContainers", "centerRect", "droppableContainer", "get", "distBetween", "push", "sort", "closestCorners", "corners", "rectCorners", "distances", "reduce", "accumulator", "corner", "index", "effectiveDistance", "Number", "toFixed", "getIntersectionRatio", "entry", "target", "max", "right", "min", "bottom", "targetArea", "entryArea", "intersectionArea", "intersectionRatio", "rectIntersection", "isPointWithinRect", "point", "pointer<PERSON><PERSON><PERSON>", "pointerCoordinates", "adjustScale", "transform", "rect1", "rect2", "scaleX", "scaleY", "getRectDelta", "createRectAdjustmentFn", "modifier", "adjustClientRect", "adjustments", "acc", "adjustment", "getAdjustedRect", "parseTransform", "startsWith", "transformArray", "slice", "split", "inverseTransform", "parsedTransform", "translateX", "translateY", "parseFloat", "indexOf", "w", "h", "defaultOptions", "ignoreTransform", "getClientRect", "element", "getBoundingClientRect", "getWindow", "getComputedStyle", "getTransformAgnosticClientRect", "getWindowClientRect", "innerWidth", "innerHeight", "isFixed", "node", "computedStyle", "position", "isScrollable", "overflowRegex", "properties", "some", "test", "getScrollableAncestors", "limit", "scrollParents", "findScrollableAncestors", "isDocument", "scrollingElement", "includes", "isHTMLElement", "isSVGElement", "parentNode", "getFirstScrollableAncestor", "firstScrollableAncestor", "getScrollableElement", "canUseDOM", "isWindow", "isNode", "getOwnerDocument", "window", "getScrollXCoordinate", "scrollX", "scrollLeft", "getScrollYCoordinate", "scrollY", "scrollTop", "getScrollCoordinates", "Direction", "isDocumentScrollingElement", "document", "getScrollPosition", "scrollingContainer", "minScroll", "dimensions", "clientHeight", "clientWidth", "maxScroll", "scrollWidth", "scrollHeight", "isTop", "isLeft", "isBottom", "isRight", "defaultThreshold", "getScrollDirectionAndSpeed", "scrollContainer", "scrollContainerRect", "acceleration", "thresholdPercentage", "direction", "speed", "threshold", "Backward", "abs", "Forward", "getScrollElementRect", "getScrollOffsets", "scrollableAncestors", "getScrollXOffset", "getScrollYOffset", "scrollIntoViewIfNeeded", "measure", "scrollIntoView", "block", "inline", "Rect", "constructor", "scrollOffsets", "axis", "keys", "getScrollOffset", "key", "defineProperty", "currentOffsets", "scrollOffsetsDeltla", "enumerable", "Listeners", "removeAll", "removeEventListener", "eventName", "handler", "addEventListener", "getEventListenerTarget", "EventTarget", "hasExceededDistance", "delta", "measurement", "dx", "dy", "EventName", "preventDefault", "stopPropagation", "KeyboardCode", "defaultKeyboardCodes", "start", "Space", "Enter", "cancel", "Esc", "end", "Tab", "defaultKeyboardCoordinateGetter", "currentCoordinates", "code", "Right", "Left", "Down", "Up", "undefined", "KeyboardSensor", "props", "autoScrollEnabled", "referenceCoordinates", "windowListeners", "handleKeyDown", "bind", "handleCancel", "attach", "handleStart", "Resize", "VisibilityChange", "setTimeout", "Keydown", "activeNode", "onStart", "current", "isKeyboardEvent", "context", "keyboardCodes", "coordinateGetter", "scroll<PERSON>eh<PERSON>or", "handleEnd", "newCoordinates", "coordinates<PERSON><PERSON><PERSON>", "getCoordinatesDelta", "scrollDelta", "scrollElementRect", "clampedCoordinates", "canScrollX", "canScrollY", "newScrollCoordinates", "canScrollToNewCoordinates", "scrollTo", "behavior", "scrollBy", "handleMove", "getAdjustedCoordinates", "coordinates", "onMove", "onEnd", "detach", "onCancel", "activators", "onActivation", "nativeEvent", "activator", "activatorNode", "isDistanceConstraint", "constraint", "Boolean", "isDelayConstraint", "AbstractPointerSensor", "events", "<PERSON><PERSON><PERSON><PERSON>", "activated", "initialCoordinates", "timeoutId", "documentListeners", "handleKeydown", "removeTextSelection", "activationConstraint", "bypassActivationConstraint", "move", "name", "passive", "DragStart", "ContextMenu", "delay", "handlePending", "clearTimeout", "offset", "onPending", "Click", "capture", "SelectionChange", "tolerance", "distance", "cancelable", "onAbort", "getSelection", "removeAllRanges", "PointerSensor", "isPrimary", "button", "MouseB<PERSON>on", "MouseSensor", "RightClick", "TouchSensor", "setup", "teardown", "touches", "AutoScrollActivator", "TraversalOrder", "useAutoScroller", "Pointer", "canScroll", "draggingRect", "enabled", "interval", "order", "TreeOrder", "scrollableAncestorRects", "scrollIntent", "useScrollIntent", "disabled", "setAutoScrollInterval", "clearAutoScrollInterval", "useInterval", "scrollSpeed", "useRef", "scrollDirection", "DraggableRect", "scrollContainerRef", "autoScroll", "sortedScrollableAncestors", "reverse", "JSON", "stringify", "defaultScrollIntent", "previousDel<PERSON>", "usePrevious", "useLazyMemo", "previousIntent", "sign", "useCachedNode", "draggableNodes", "draggableNode", "cachedNode", "useCombineActivators", "getSyntheticHandler", "Sensor", "sensorActivators", "map", "MeasuringStrategy", "MeasuringFrequency", "defaultValue", "Map", "useDroppableMeasuring", "containers", "dragging", "dependencies", "config", "queue", "setQueue", "frequency", "strategy", "containersRef", "isDisabled", "disabledRef", "useLatestValue", "measureDroppableContainers", "ids", "concat", "previousValue", "set", "measuringScheduled", "Always", "BeforeDragging", "useInitialValue", "computeFn", "useInitialRect", "useMutationObserver", "callback", "handleMutations", "useEvent", "mutationObserver", "MutationObserver", "disconnect", "useResizeObserver", "handleResize", "resizeObserver", "ResizeObserver", "defaultMeasure", "useRect", "fallbackRect", "setRect", "measureRect", "currentRect", "isConnected", "newRect", "records", "record", "HTMLElement", "contains", "useIsomorphicLayoutEffect", "observe", "body", "childList", "subtree", "useRectDelta", "initialRect", "useScrollableAncestors", "previousNode", "ancestors", "useScrollOffsets", "elements", "scrollCoordinates", "setScrollCoordinates", "prevElements", "handleScroll", "previousElements", "cleanup", "entries", "scrollableElement", "Array", "from", "values", "useScrollOffsetsDelta", "initialScrollOffsets", "hasScrollOffsets", "subtract", "useSensorSetup", "teardownFns", "useSyntheticListeners", "useWindowRect", "useRects", "firstElement", "windowRect", "rects", "setRects", "measureRects", "getMeasurableNode", "children", "<PERSON><PERSON><PERSON><PERSON>", "useDragOverlayMeasuring", "handleNodeChange", "nodeRef", "setRef", "useNodeRef", "defaultSensors", "defaultData", "defaultMeasuringConfiguration", "droppable", "WhileDragging", "Optimized", "dragOverlay", "DroppableContainersMap", "toArray", "getEnabled", "getNodeFor", "defaultPublicContext", "activatorEvent", "activeNodeRect", "containerNodeRect", "measuringConfiguration", "defaultInternalContext", "ariaDescribedById", "InternalContext", "PublicContext", "getInitialState", "nodes", "translate", "reducer", "state", "action", "<PERSON><PERSON><PERSON><PERSON>", "DragEnd", "DragCancel", "RegisterDroppable", "SetDroppableDisabled", "UnregisterDroppable", "RestoreFocus", "previousActivatorEvent", "previousActiveId", "activeElement", "requestAnimationFrame", "focusableNode", "findFirstFocusableNode", "focus", "applyModifiers", "modifiers", "args", "useMeasuringConfiguration", "useLayoutShiftScrollCompensation", "initialized", "rectD<PERSON><PERSON>", "ActiveDraggableContext", "Status", "DndContext", "memo", "accessibility", "collisionDetection", "measuring", "store", "useReducer", "dispatchMonitorEvent", "registerMonitorListener", "status", "setStatus", "Uninitialized", "isInitialized", "Initialized", "activeId", "activeRects", "initial", "translated", "activeRef", "activeSensor", "setActiveSensor", "setActivatorEvent", "latestProps", "draggableDescribedById", "enabledDroppableContainers", "activationCoordinates", "autoScrollOptions", "getAutoScrollerOptions", "initialActiveNodeRect", "layoutShiftCompensation", "parentElement", "sensorContext", "draggingNode", "draggingNodeRect", "scrollAdjustedTranslate", "overNode", "usesDragOverlay", "nodeRectDelta", "modifiedTranslate", "overlayNodeRect", "scrollAdjustment", "activeNodeScrollDelta", "overId", "setOver", "appliedTranslate", "activeSensorRef", "instantiateSensor", "sensorInstance", "onDragAbort", "onDragPending", "unstable_batchedUpdates", "Initializing", "createHandler", "cancelDrop", "shouldCancel", "Promise", "resolve", "bindActivatorToSensorInstantiator", "activeDraggableNode", "dndKit", "defaultPrevented", "activationContext", "shouldActivate", "capturedBy", "over<PERSON><PERSON><PERSON>", "publicContext", "internalContext", "Provider", "restoreFocus", "activeSensorDisablesAutoscroll", "autoScrollGloballyDisabled", "NullContext", "defaultRole", "ID_PREFIX", "useDraggable", "attributes", "role", "roleDescription", "tabIndex", "isDragging", "setNodeRef", "setActivatorNodeRef", "dataRef", "memoizedAttributes", "useDndContext", "defaultResizeObserverConfig", "timeout", "useDroppable", "resizeObserverConfig", "previous", "resizeObserverConnected", "callbackId", "resizeObserverDisabled", "updateMeasurementsFor", "resizeObserverTimeout", "isArray", "newElement", "previousElement", "unobserve", "isOver", "AnimationManager", "animation", "cloned<PERSON><PERSON><PERSON><PERSON>", "setClonedChildren", "setElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "then", "cloneElement", "ref", "defaultTransform", "NullifiedContextProvider", "baseStyles", "touchAction", "defaultTransition", "isKeyboardActivator", "PositionedOverlay", "forwardRef", "as", "className", "style", "transition", "scaleAdjustedTransform", "styles", "CSS", "Transform", "toString", "createElement", "defaultDropAnimationSideEffects", "originalStyles", "getPropertyValue", "setProperty", "classList", "remove", "defaultKeyframeResolver", "final", "defaultDropAnimationConfiguration", "duration", "easing", "keyframes", "sideEffects", "opacity", "useDropAnimation", "activeDraggable", "measurableNode", "createDefaultDropAnimation", "rest", "scale", "finalTransform", "animationKeyframes", "firstKeyframe", "lastKeyframe", "animate", "fill", "onfinish", "useKey", "DragOverlay", "dropAnimation", "dropAnimationConfig", "wrapperElement", "zIndex", "modifiedTransform"]}