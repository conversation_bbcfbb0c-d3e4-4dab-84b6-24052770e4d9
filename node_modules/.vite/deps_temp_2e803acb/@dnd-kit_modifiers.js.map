{"version": 3, "sources": ["../../@dnd-kit/modifiers/src/createSnapModifier.ts", "../../@dnd-kit/modifiers/src/restrictToHorizontalAxis.ts", "../../@dnd-kit/modifiers/src/utilities/restrictToBoundingRect.ts", "../../@dnd-kit/modifiers/src/restrictToParentElement.ts", "../../@dnd-kit/modifiers/src/restrictToFirstScrollableAncestor.ts", "../../@dnd-kit/modifiers/src/restrictToVerticalAxis.ts", "../../@dnd-kit/modifiers/src/restrictToWindowEdges.ts", "../../@dnd-kit/modifiers/src/snapCenterToCursor.ts"], "sourcesContent": ["import type {Modifier} from '@dnd-kit/core';\n\nexport function createSnapModifier(gridSize: number): Modifier {\n  return ({transform}) => ({\n    ...transform,\n    x: Math.ceil(transform.x / gridSize) * gridSize,\n    y: Math.ceil(transform.y / gridSize) * gridSize,\n  });\n}\n", "import type {Modifier} from '@dnd-kit/core';\n\nexport const restrictToHorizontalAxis: Modifier = ({transform}) => {\n  return {\n    ...transform,\n    y: 0,\n  };\n};\n", "import type {ClientRect} from '@dnd-kit/core';\nimport type {Transform} from '@dnd-kit/utilities';\n\nexport function restrictToBoundingRect(\n  transform: Transform,\n  rect: ClientRect,\n  boundingRect: ClientRect\n): Transform {\n  const value = {\n    ...transform,\n  };\n\n  if (rect.top + transform.y <= boundingRect.top) {\n    value.y = boundingRect.top - rect.top;\n  } else if (\n    rect.bottom + transform.y >=\n    boundingRect.top + boundingRect.height\n  ) {\n    value.y = boundingRect.top + boundingRect.height - rect.bottom;\n  }\n\n  if (rect.left + transform.x <= boundingRect.left) {\n    value.x = boundingRect.left - rect.left;\n  } else if (\n    rect.right + transform.x >=\n    boundingRect.left + boundingRect.width\n  ) {\n    value.x = boundingRect.left + boundingRect.width - rect.right;\n  }\n\n  return value;\n}\n", "import type {Modifier} from '@dnd-kit/core';\nimport {restrictToBoundingRect} from './utilities';\n\nexport const restrictToParentElement: Modifier = ({\n  containerNodeRect,\n  draggingNodeRect,\n  transform,\n}) => {\n  if (!draggingNodeRect || !containerNodeRect) {\n    return transform;\n  }\n\n  return restrictToBoundingRect(transform, draggingNodeRect, containerNodeRect);\n};\n", "import type {Modifier} from '@dnd-kit/core';\nimport {restrictToBoundingRect} from './utilities';\n\nexport const restrictToFirstScrollableAncestor: Modifier = ({\n  draggingNodeRect,\n  transform,\n  scrollableAncestorRects,\n}) => {\n  const firstScrollableAncestorRect = scrollableAncestorRects[0];\n\n  if (!draggingNodeRect || !firstScrollableAncestorRect) {\n    return transform;\n  }\n\n  return restrictToBoundingRect(\n    transform,\n    draggingNodeRect,\n    firstScrollableAncestorRect\n  );\n};\n", "import type {Modifier} from '@dnd-kit/core';\n\nexport const restrictToVerticalAxis: Modifier = ({transform}) => {\n  return {\n    ...transform,\n    x: 0,\n  };\n};\n", "import type {Modifier} from '@dnd-kit/core';\n\nimport {restrictToBoundingRect} from './utilities';\n\nexport const restrictToWindowEdges: Modifier = ({\n  transform,\n  draggingNodeRect,\n  windowRect,\n}) => {\n  if (!draggingNodeRect || !windowRect) {\n    return transform;\n  }\n\n  return restrictToBoundingRect(transform, draggingNodeRect, windowRect);\n};\n", "import type {Modifier} from '@dnd-kit/core';\nimport {getEventCoordinates} from '@dnd-kit/utilities';\n\nexport const snapCenterToCursor: Modifier = ({\n  activatorEvent,\n  draggingNodeRect,\n  transform,\n}) => {\n  if (draggingNodeRect && activatorEvent) {\n    const activatorCoordinates = getEventCoordinates(activatorEvent);\n\n    if (!activatorCoordinates) {\n      return transform;\n    }\n\n    const offsetX = activatorCoordinates.x - draggingNodeRect.left;\n    const offsetY = activatorCoordinates.y - draggingNodeRect.top;\n\n    return {\n      ...transform,\n      x: transform.x + offsetX - draggingNodeRect.width / 2,\n      y: transform.y + offsetY - draggingNodeRect.height / 2,\n    };\n  }\n\n  return transform;\n};\n"], "mappings": ";;;;;;;SAEgBA,mBAAmBC,UAAAA;AACjC,SAAO,UAAA;AAAA,QAAC;MAACC;QAAF;AAAA,WAAkB;MACvB,GAAGA;MACHC,GAAGC,KAAKC,KAAKH,UAAUC,IAAIF,QAAxB,IAAoCA;MACvCK,GAAGF,KAAKC,KAAKH,UAAUI,IAAIL,QAAxB,IAAoCA;;;AAE1C;ICNYM,2BAAqC,UAAA;MAAC;IAACL;;AAClD,SAAO;IACL,GAAGA;IACHI,GAAG;;AAEN;SCJeE,uBACdN,WACAO,MACAC,cAAAA;AAEA,QAAMC,QAAQ;IACZ,GAAGT;;AAGL,MAAIO,KAAKG,MAAMV,UAAUI,KAAKI,aAAaE,KAAK;AAC9CD,UAAML,IAAII,aAAaE,MAAMH,KAAKG;aAElCH,KAAKI,SAASX,UAAUI,KACxBI,aAAaE,MAAMF,aAAaI,QAChC;AACAH,UAAML,IAAII,aAAaE,MAAMF,aAAaI,SAASL,KAAKI;;AAG1D,MAAIJ,KAAKM,OAAOb,UAAUC,KAAKO,aAAaK,MAAM;AAChDJ,UAAMR,IAAIO,aAAaK,OAAON,KAAKM;aAEnCN,KAAKO,QAAQd,UAAUC,KACvBO,aAAaK,OAAOL,aAAaO,OACjC;AACAN,UAAMR,IAAIO,aAAaK,OAAOL,aAAaO,QAAQR,KAAKO;;AAG1D,SAAOL;AACR;IC5BYO,0BAAoC,UAAA;MAAC;IAChDC;IACAC;IACAlB;;AAEA,MAAI,CAACkB,oBAAoB,CAACD,mBAAmB;AAC3C,WAAOjB;;AAGT,SAAOM,uBAAuBN,WAAWkB,kBAAkBD,iBAA9B;AAC9B;ICVYE,oCAA8C,UAAA;MAAC;IAC1DD;IACAlB;IACAoB;;AAEA,QAAMC,8BAA8BD,wBAAwB,CAAD;AAE3D,MAAI,CAACF,oBAAoB,CAACG,6BAA6B;AACrD,WAAOrB;;AAGT,SAAOM,uBACLN,WACAkB,kBACAG,2BAH2B;AAK9B;ICjBYC,yBAAmC,UAAA;MAAC;IAACtB;;AAChD,SAAO;IACL,GAAGA;IACHC,GAAG;;AAEN;ICHYsB,wBAAkC,UAAA;MAAC;IAC9CvB;IACAkB;IACAM;;AAEA,MAAI,CAACN,oBAAoB,CAACM,YAAY;AACpC,WAAOxB;;AAGT,SAAOM,uBAAuBN,WAAWkB,kBAAkBM,UAA9B;AAC9B;ICXYC,qBAA+B,UAAA;MAAC;IAC3CC;IACAR;IACAlB;;AAEA,MAAIkB,oBAAoBQ,gBAAgB;AACtC,UAAMC,uBAAuBC,oBAAoBF,cAAD;AAEhD,QAAI,CAACC,sBAAsB;AACzB,aAAO3B;;AAGT,UAAM6B,UAAUF,qBAAqB1B,IAAIiB,iBAAiBL;AAC1D,UAAMiB,UAAUH,qBAAqBvB,IAAIc,iBAAiBR;AAE1D,WAAO;MACL,GAAGV;MACHC,GAAGD,UAAUC,IAAI4B,UAAUX,iBAAiBH,QAAQ;MACpDX,GAAGJ,UAAUI,IAAI0B,UAAUZ,iBAAiBN,SAAS;;;AAIzD,SAAOZ;AACR;", "names": ["createSnapModifier", "gridSize", "transform", "x", "Math", "ceil", "y", "restrictToHorizontalAxis", "restrictToBoundingRect", "rect", "boundingRect", "value", "top", "bottom", "height", "left", "right", "width", "restrictToParentElement", "containerNodeRect", "draggingNodeRect", "restrictToFirstScrollableAncestor", "scrollableAncestorRects", "firstScrollableAncestorRect", "restrictToVerticalAxis", "restrictToWindowEdges", "windowRect", "snapCenterToCursor", "activatorEvent", "activatorCoordinates", "getEventCoordinates", "offsetX", "offsetY"]}