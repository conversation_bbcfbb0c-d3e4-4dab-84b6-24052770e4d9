import { app, <PERSON><PERSON>erWindow, ipc<PERSON><PERSON>, <PERSON>u } from 'electron';
import { join, resolve } from 'path';
import { isDev } from './utils/environment';
import { DatabaseConnection } from './database/connection';
import { CryptographyService } from './auth/crypto.service';
import { MCPService } from './mcp/service';
import { authService } from './auth/auth.service';
import { todoDAO } from './dao/todo.dao';
import { categoryDAO } from './dao/category.dao';
import { createAPIResponse } from './api';
import { TodoValidationError, TodoDatabaseError, TodoBusinessError } from '@shared/types';
import log from 'electron-log';

// Configure logging
log.transports.file.level = 'info';
log.transports.console.level = 'debug';

/**
 * Enhanced error handling for IPC operations
 */
function handleIpcError(error: unknown, operation: string): any {
  log.error(`${operation} error:`, error);

  if (error instanceof TodoValidationError) {
    // Include validation details in the error message for better user feedback
    const errorMessage = error.getUserMessage();
    const detailedMessage = error.validationErrors.length > 0
      ? `${errorMessage} (${error.code})`
      : errorMessage;
    return createAPIResponse(false, null, detailedMessage);
  }

  if (error instanceof TodoDatabaseError) {
    const errorMessage = error.getUserMessage();
    const detailedMessage = `${errorMessage} (${error.code})`;
    return createAPIResponse(false, null, detailedMessage);
  }

  if (error instanceof TodoBusinessError) {
    const detailedMessage = `${error.message} (${error.code})`;
    return createAPIResponse(false, null, detailedMessage);
  }

  // Generic error handling
  const message = error instanceof Error ? error.message : 'Unknown error occurred';
  return createAPIResponse(false, null, message);
}

class ModernTodoApp {
  private mainWindow: BrowserWindow | null = null;
  private databaseService: DatabaseConnection | null = null;
  private securityService: CryptographyService | null = null;
  private mcpService: MCPService | null = null;

  constructor() {
    this.setupApp();
    this.setupServices();
    this.setupIpcHandlers();
  }

  private setupApp(): void {
    // Set app user model id for Windows
    app.setAppUserModelId('com.moderntodo.app');

    // Handle app events
    app.whenReady().then(() => {
      this.createMainWindow();
      this.setupMenu();
      
      app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
          this.createMainWindow();
        }
      });
    });

    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });

    app.on('before-quit', async () => {
      await this.cleanup();
    });
  }

  private async setupServices(): Promise<void> {
    try {
      // Initialize core services
      this.securityService = new CryptographyService();
      this.databaseService = DatabaseConnection.getInstance();
      this.mcpService = MCPService.getInstance();

      // Initialize services in order
      await this.databaseService.initialize();
      log.info('Database service initialized');

      await this.mcpService.initialize();
      log.info('MCP service initialized');

    } catch (error) {
      log.error('Failed to initialize services:', error);
      app.quit();
    }
  }

  private createMainWindow(): void {
    // Linux-friendly window configuration
    const windowConfig: Electron.BrowserWindowConstructorOptions = {
      width: 1200,
      height: 800,
      minWidth: 800,
      minHeight: 600,
      show: false,
      frame: true, // Enable native window frame for Linux
      titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default',
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: join(__dirname, '../preload/index.js'),
        webSecurity: true,
        allowRunningInsecureContent: false,
        devTools: isDev(),
        sandbox: false, // Required for preload script access
        experimentalFeatures: false,
      },
      icon: process.platform === 'linux' ? join(__dirname, '../../assets/icon.png') : undefined,
      autoHideMenuBar: false, // Show menu bar on Linux
      resizable: true,
      maximizable: true,
      minimizable: true,
      closable: true,
    };

    // Add platform-specific enhancements
    if (process.platform === 'darwin') {
      windowConfig.vibrancy = 'under-window';
      windowConfig.transparent = true;
    } else if (process.platform === 'win32') {
      windowConfig.backgroundMaterial = 'acrylic';
      windowConfig.transparent = true;
    }
    // Linux uses default solid background for better compatibility

    this.mainWindow = new BrowserWindow(windowConfig);

    // Set window title
    this.mainWindow.setTitle('Modern Todo App');

    // Set Content Security Policy for security
    this.mainWindow.webContents.session.webRequest.onHeadersReceived((details, callback) => {
      callback({
        responseHeaders: {
          ...details.responseHeaders,
          'Content-Security-Policy': [
            isDev()
              ? "default-src 'self' 'unsafe-inline' 'unsafe-eval' http://localhost:5173 ws://localhost:5173; img-src 'self' data: blob:; font-src 'self' data:;"
              : "default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; font-src 'self' data:; connect-src 'self';"
          ]
        }
      });
    });

    // Load the renderer
    if (isDev()) {
      // Try to load Vite dev server first
      this.mainWindow.loadURL('http://localhost:5173').catch((error) => {
        log.error('Failed to load dev server:', error);
        // Fallback to built version if dev server fails and window still exists
        if (this.mainWindow && !this.mainWindow.isDestroyed()) {
          log.info('Falling back to built renderer files');
          const rendererPath = resolve(__dirname, '../../renderer/index.html');
          log.info('Loading renderer from:', rendererPath);
          this.mainWindow.loadFile(rendererPath).catch((fallbackError) => {
            log.error('Failed to load built renderer:', fallbackError);
          });
        }
      });
    } else {
      const rendererPath = resolve(__dirname, '../../renderer/index.html');
      log.info('Loading renderer from:', rendererPath);
      this.mainWindow.loadFile(rendererPath).catch((error) => {
        log.error('Failed to load built renderer:', error);
      });
    }

    // Show window when ready
    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow?.show();
      this.mainWindow?.focus();

      if (isDev()) {
        this.mainWindow?.webContents.openDevTools({ mode: 'detach' });
      }
    });

    // Handle window closed
    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });

    // Linux-specific window behavior
    if (process.platform === 'linux') {
      // Ensure proper window management
      this.mainWindow.on('minimize', () => {
        log.debug('Window minimized');
      });

      this.mainWindow.on('maximize', () => {
        log.debug('Window maximized');
      });

      this.mainWindow.on('unmaximize', () => {
        log.debug('Window unmaximized');
      });
    }
  }

  private setupMenu(): void {
    const template: Electron.MenuItemConstructorOptions[] = [
      {
        label: 'File',
        submenu: [
          {
            label: 'New Todo',
            accelerator: 'CmdOrCtrl+N',
            click: () => {
              this.mainWindow?.webContents.send('menu-new-todo');
            },
          },
          { type: 'separator' },
          {
            label: 'Import',
            accelerator: 'CmdOrCtrl+I',
            click: () => {
              this.mainWindow?.webContents.send('menu-import');
            },
          },
          {
            label: 'Export',
            accelerator: 'CmdOrCtrl+E',
            click: () => {
              this.mainWindow?.webContents.send('menu-export');
            },
          },
          { type: 'separator' },
          {
            label: 'Quit',
            accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
            click: () => {
              app.quit();
            },
          },
        ],
      },
      {
        label: 'Edit',
        submenu: [
          { role: 'undo' },
          { role: 'redo' },
          { type: 'separator' },
          { role: 'cut' },
          { role: 'copy' },
          { role: 'paste' },
          { role: 'selectAll' },
        ],
      },
      {
        label: 'View',
        submenu: [
          { role: 'reload' },
          { role: 'forceReload' },
          { role: 'toggleDevTools' },
          { type: 'separator' },
          { role: 'resetZoom' },
          { role: 'zoomIn' },
          { role: 'zoomOut' },
          { type: 'separator' },
          { role: 'togglefullscreen' },
        ],
      },
      {
        label: 'Window',
        submenu: [
          { role: 'minimize' },
          { role: 'close' },
        ],
      },
    ];

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
  }

  private setupIpcHandlers(): void {
    // Database operations
    ipcMain.handle('db:query', async (event: any, sql: string, params?: any[]) => {
      try {
        return await this.databaseService?.executeQuery(sql, params);
      } catch (error) {
        log.error('Database query error:', error);
        throw error;
      }
    });

    ipcMain.handle('db:transaction', async (event: any, operations: any[]) => {
      try {
        return await this.databaseService?.executeTransaction(async (execute) => {
          const results = [];
          for (const op of operations) {
            results.push(await execute(op.query, op.params));
          }
          return results;
        });
      } catch (error) {
        log.error('Database transaction error:', error);
        throw error;
      }
    });

    // Security operations
    ipcMain.handle('security:encrypt', async (event: any, data: string, password: string) => {
      try {
        return await this.securityService?.encryptData(data, password);
      } catch (error) {
        log.error('Encryption error:', error);
        throw error;
      }
    });

    ipcMain.handle('security:decrypt', async (event: any, encryptedData: any, password: string) => {
      try {
        return await this.securityService?.decryptData(encryptedData, password);
      } catch (error) {
        log.error('Decryption error:', error);
        throw error;
      }
    });

    // MCP operations
    ipcMain.handle('mcp:sync', async (event: any, data: any) => {
      try {
        return await this.mcpService?.executeQuery(data.sql, data.params);
      } catch (error) {
        log.error('MCP sync error:', error);
        throw error;
      }
    });

    // System operations
    ipcMain.handle('system:getInfo', async () => {
      return {
        platform: process.platform,
        arch: process.arch,
        version: app.getVersion(),
        electronVersion: process.versions.electron,
        nodeVersion: process.versions.node,
      };
    });

    ipcMain.handle('app:quit', () => {
      app.quit();
    });

    ipcMain.handle('app:minimize', () => {
      this.mainWindow?.minimize();
    });

    ipcMain.handle('app:maximize', () => {
      if (this.mainWindow?.isMaximized()) {
        this.mainWindow.unmaximize();
      } else {
        this.mainWindow?.maximize();
      }
    });

    ipcMain.handle('app:close', () => {
      this.mainWindow?.close();
    });

    // Todo operations
    ipcMain.handle('todos:getAll', async (event: any, sessionId: string, filters?: any, pagination?: any) => {
      try {
        const session = await authService.validateSession(sessionId);
        if (!session) {
          throw new Error('Invalid session');
        }

        const result = await todoDAO.findByUserId(session.userId, pagination, filters);
        return createAPIResponse(true, result);
      } catch (error) {
        log.error('Get todos error:', error);
        return createAPIResponse(false, null, error instanceof Error ? error.message : 'Unknown error');
      }
    });

    ipcMain.handle('todos:create', async (event: any, sessionId: string, todoData: any) => {
      try {
        const session = await authService.validateSession(sessionId);
        if (!session) {
          throw new Error('Invalid session');
        }

        const todo = await todoDAO.createTodo(todoData, session.userId);
        return createAPIResponse(true, todo);
      } catch (error) {
        return handleIpcError(error, 'Create todo');
      }
    });

    ipcMain.handle('todos:update', async (event: any, sessionId: string, todoId: string, updates: any) => {
      try {
        const session = await authService.validateSession(sessionId);
        if (!session) {
          throw new Error('Invalid session');
        }

        const todo = await todoDAO.updateTodo(todoId, updates, session.userId);
        return createAPIResponse(true, todo);
      } catch (error) {
        return handleIpcError(error, 'Update todo');
      }
    });

    ipcMain.handle('todos:delete', async (event: any, sessionId: string, todoId: string) => {
      try {
        const session = await authService.validateSession(sessionId);
        if (!session) {
          throw new Error('Invalid session');
        }

        const todo = await todoDAO.softDeleteTodo(todoId, session.userId);
        return createAPIResponse(true, todo);
      } catch (error) {
        return handleIpcError(error, 'Delete todo');
      }
    });

    ipcMain.handle('todos:updateStatus', async (event: any, sessionId: string, todoId: string, status: string) => {
      try {
        const session = await authService.validateSession(sessionId);
        if (!session) {
          throw new Error('Invalid session');
        }

        const todo = await todoDAO.updateStatus(todoId, status as any, session.userId);
        return createAPIResponse(true, todo);
      } catch (error) {
        return handleIpcError(error, 'Update todo status');
      }
    });

    // Category operations
    ipcMain.handle('categories:getAll', async (event: any, sessionId: string) => {
      try {
        const session = await authService.validateSession(sessionId);
        if (!session) {
          throw new Error('Invalid session');
        }

        const categories = await categoryDAO.findByUserId(session.userId);
        return createAPIResponse(true, categories);
      } catch (error) {
        return handleIpcError(error, 'Get categories');
      }
    });

    ipcMain.handle('categories:create', async (event: any, sessionId: string, categoryData: any) => {
      try {
        const session = await authService.validateSession(sessionId);
        if (!session) {
          throw new Error('Invalid session');
        }

        const category = await categoryDAO.createCategory(
          session.userId,
          categoryData.name,
          categoryData.color,
          categoryData.icon,
          categoryData.is_default
        );
        return createAPIResponse(true, category);
      } catch (error) {
        return handleIpcError(error, 'Create category');
      }
    });

    ipcMain.handle('categories:update', async (event: any, sessionId: string, categoryId: string, updates: any) => {
      try {
        const session = await authService.validateSession(sessionId);
        if (!session) {
          throw new Error('Invalid session');
        }

        const category = await categoryDAO.updateCategory(categoryId, session.userId, updates);
        return createAPIResponse(true, category);
      } catch (error) {
        return handleIpcError(error, 'Update category');
      }
    });

    ipcMain.handle('categories:delete', async (event: any, sessionId: string, categoryId: string) => {
      try {
        const session = await authService.validateSession(sessionId);
        if (!session) {
          throw new Error('Invalid session');
        }

        const success = await categoryDAO.deleteCategory(categoryId, session.userId);
        return createAPIResponse(true, { success });
      } catch (error) {
        return handleIpcError(error, 'Delete category');
      }
    });

    ipcMain.handle('categories:reorder', async (event: any, sessionId: string, categoryOrders: any[]) => {
      try {
        const session = await authService.validateSession(sessionId);
        if (!session) {
          throw new Error('Invalid session');
        }

        const categories = await categoryDAO.reorderCategories(session.userId, categoryOrders);
        return createAPIResponse(true, categories);
      } catch (error) {
        return handleIpcError(error, 'Reorder categories');
      }
    });

    // Tag operations
    ipcMain.handle('tags:getAll', async (event: any, sessionId: string) => {
      try {
        const session = await authService.validateSession(sessionId);
        if (!session) {
          throw new Error('Invalid session');
        }

        const tags = await todoDAO.getAllTags(session.userId);
        return createAPIResponse(true, tags);
      } catch (error) {
        return handleIpcError(error, 'Get all tags');
      }
    });

    ipcMain.handle('tags:getSuggestions', async (event: any, sessionId: string, query: string, limit?: number) => {
      try {
        const session = await authService.validateSession(sessionId);
        if (!session) {
          throw new Error('Invalid session');
        }

        const suggestions = await todoDAO.getTagSuggestions(session.userId, query, limit);
        return createAPIResponse(true, suggestions);
      } catch (error) {
        return handleIpcError(error, 'Get tag suggestions');
      }
    });

    ipcMain.handle('tags:getPopular', async (event: any, sessionId: string, limit?: number) => {
      try {
        const session = await authService.validateSession(sessionId);
        if (!session) {
          throw new Error('Invalid session');
        }

        const popularTags = await todoDAO.getPopularTags(session.userId, limit);
        return createAPIResponse(true, popularTags);
      } catch (error) {
        return handleIpcError(error, 'Get popular tags');
      }
    });

    ipcMain.handle('tags:getRecent', async (event: any, sessionId: string, limit?: number) => {
      try {
        const session = await authService.validateSession(sessionId);
        if (!session) {
          throw new Error('Invalid session');
        }

        const recentTags = await todoDAO.getRecentTags(session.userId, limit);
        return createAPIResponse(true, recentTags);
      } catch (error) {
        return handleIpcError(error, 'Get recent tags');
      }
    });

    ipcMain.handle('tags:getStats', async (event: any, sessionId: string, tag: string) => {
      try {
        const session = await authService.validateSession(sessionId);
        if (!session) {
          throw new Error('Invalid session');
        }

        const stats = await todoDAO.getTagStats(session.userId, tag);
        return createAPIResponse(true, stats);
      } catch (error) {
        return handleIpcError(error, 'Get tag stats');
      }
    });
  }

  private async cleanup(): Promise<void> {
    try {
      await this.databaseService?.close();
      await this.mcpService?.disconnect();
      log.info('Application cleanup completed');
    } catch (error) {
      log.error('Cleanup error:', error);
    }
  }
}

// Create and initialize the application
new ModernTodoApp();