// Import app from electron
import { app } from 'electron';

export const isDev = (): boolean => {
  // Respect NODE_ENV first, then check if app is packaged
  if (process.env.NODE_ENV === 'production') {
    return false;
  }
  if (process.env.NODE_ENV === 'development') {
    return true;
  }
  // Fallback to checking if app is packaged
  return !app.isPackaged;
};

export const getAppPath = (): string => {
  return app.getAppPath();
};

export const getUserDataPath = (): string => {
  return app.getPath('userData');
};

export const getLogsPath = (): string => {
  return app.getPath('logs');
};