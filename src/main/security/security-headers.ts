import { <PERSON>rowserWindow } from 'electron';
import { securityConfig } from './security-config';

export interface SecurityHeaders {
  [key: string]: string;
}

export class SecurityHeadersManager {
  private static instance: SecurityHeadersManager;
  private headers: SecurityHeaders;

  private constructor() {
    this.headers = this.buildSecurityHeaders();
  }

  public static getInstance(): SecurityHeadersManager {
    if (!SecurityHeadersManager.instance) {
      SecurityHeadersManager.instance = new SecurityHeadersManager();
    }
    return SecurityHeadersManager.instance;
  }

  private buildSecurityHeaders(): SecurityHeaders {
    const config = securityConfig.getSecurityConfig();
    
    return {
      // Strict Transport Security
      'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
      
      // Content Type Options
      'X-Content-Type-Options': 'nosniff',
      
      // Frame Options
      'X-Frame-Options': 'DENY',
      
      // XSS Protection
      'X-XSS-Protection': '1; mode=block',
      
      // Referrer Policy
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      
      // Permissions Policy
      'Permissions-Policy': 'camera=(), microphone=(), geolocation=(), payment=()',
      
      // Cross-Origin Policies
      'Cross-Origin-Embedder-Policy': 'require-corp',
      'Cross-Origin-Opener-Policy': 'same-origin',
      'Cross-Origin-Resource-Policy': 'same-origin',
      
      // Content Security Policy (enhanced)
      'Content-Security-Policy': this.buildEnhancedCSP(),
      
      // Security headers for API responses
      'X-Permitted-Cross-Domain-Policies': 'none',
      'X-Download-Options': 'noopen',
      'Cache-Control': 'no-store, no-cache, must-revalidate, private'
    };
  }

  private buildEnhancedCSP(): string {
    const directives = [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline'",
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
      "style-src-elem 'self' 'unsafe-inline' https://fonts.googleapis.com",
      "img-src 'self' data: https:",
      "font-src 'self' https://fonts.gstatic.com data:",
      "connect-src 'self' https://api.motherduck.com https://fonts.googleapis.com https://fonts.gstatic.com",
      "object-src 'none'",
      "base-uri 'self'",
      "form-action 'self'",
      "frame-ancestors 'none'",
      "upgrade-insecure-requests",
      "block-all-mixed-content"
    ];

    return directives.join('; ');
  }

  public applyHeaders(window: BrowserWindow): void {
    const webContents = window.webContents;
    
    webContents.session.webRequest.onHeadersReceived((details, callback) => {
      const responseHeaders = { ...details.responseHeaders };
      
      // Apply security headers
      for (const [header, value] of Object.entries(this.headers)) {
        responseHeaders[header] = [value];
      }
      
      callback({ responseHeaders });
    });
  }

  public getHeaders(): SecurityHeaders {
    return { ...this.headers };
  }
}

export const securityHeaders = SecurityHeadersManager.getInstance();
