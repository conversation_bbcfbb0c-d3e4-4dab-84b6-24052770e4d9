import { app, BrowserWindow, session } from 'electron';
import { securityConfig } from './security-config';
import { ipcSecurity } from './ipc-security';
import { auditLogger, AuditEventType, AuditSeverity } from './audit-logger';
import { intrusionDetection } from './intrusion-detection';
import { sessionSecurity } from './session-security';
import { rateLimiter } from './rate-limiter';
import { inputValidator } from './input-validator';
import { runtimeSecurityMonitor } from './runtime-security-monitor';
import { secureUpdater } from './secure-updater';
import { secureBackupManager } from './secure-backup';
import { certificatePinning } from './certificate-pinning';
import { securityHeaders } from './security-headers';

export class SecurityManager {
  private static instance: SecurityManager;
  private isInitialized = false;

  private constructor() {}

  public static getInstance(): SecurityManager {
    if (!SecurityManager.instance) {
      SecurityManager.instance = new SecurityManager();
    }
    return SecurityManager.instance;
  }

  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      console.log('🔒 Initializing Security Manager...');

      // Validate security configuration
      securityConfig.validateSecurityConfig();
      console.log('✅ Security configuration validated');

      // Initialize Electron security
      await this.initializeElectronSecurity();
      console.log('✅ Electron security initialized');

      // Initialize security services
      await this.initializeSecurityServices();
      console.log('✅ Security services initialized');

      // Set up security monitoring
      await this.setupSecurityMonitoring();
      console.log('✅ Security monitoring enabled');

      // Initialize production security hardening
      await this.initializeProductionSecurity();
      console.log('✅ Production security hardening enabled');

      // Log security initialization
      await auditLogger.log({
        type: AuditEventType.APPLICATION_START,
        severity: AuditSeverity.LOW,
        success: true,
        details: {
          securityInitialized: true,
          productionSecure: securityConfig.isProductionSecure(),
          timestamp: Date.now(),
        },
      });

      this.isInitialized = true;
      console.log('🔒 Security Manager initialized successfully');

    } catch (error) {
      console.error('❌ Failed to initialize Security Manager:', error);
      
      await auditLogger.log({
        type: AuditEventType.APPLICATION_START,
        severity: AuditSeverity.CRITICAL,
        success: false,
        errorMessage: error instanceof Error ? error.message : String(error),
        details: {
          securityInitializationFailed: true,
        },
      });

      throw error;
    }
  }

  private async initializeElectronSecurity(): Promise<void> {
    const config = securityConfig.getSecurityConfig();

    // Configure app security
    app.on('web-contents-created', (event, contents) => {
      // Prevent navigation to external URLs
      contents.on('will-redirect', (navigationEvent, navigationURL) => {
        const allowedOrigins = config.allowedOrigins;
        const isAllowed = allowedOrigins.some(origin =>
          navigationURL.startsWith(origin) || origin.includes('*')
        );

        if (!isAllowed) {
          navigationEvent.preventDefault();
          console.warn(`🚫 Blocked redirect to: ${navigationURL}`);
        }
      });

      // Prevent opening external links
      contents.setWindowOpenHandler(({ url }) => {
        console.warn(`🚫 Blocked window.open to: ${url}`);
        return { action: 'deny' };
      });
    });

    // Configure session security
    app.on('session-created', (session) => {
      this.configureSessionSecurity(session);
    });

    // Configure default session
    this.configureSessionSecurity(session.defaultSession);
  }

  private configureSessionSecurity(electronSession: Electron.Session): void {
    const config = securityConfig.getSecurityConfig();

    // Set up Content Security Policy
    if (config.cspEnabled) {
      const csp = this.buildContentSecurityPolicy();
      electronSession.webRequest.onHeadersReceived((details, callback) => {
        // In development, don't override CSP if it's already set in HTML
        const responseHeaders = { ...details.responseHeaders };

        // Only set CSP if not already present or if we're in production
        if (!responseHeaders['Content-Security-Policy'] || !securityConfig.isDevelopment()) {
          responseHeaders['Content-Security-Policy'] = [csp];
        }

        callback({ responseHeaders });
      });
    }

    // Set up security headers
    if (config.enableSecurityHeaders) {
      electronSession.webRequest.onHeadersReceived((details, callback) => {
        callback({
          responseHeaders: {
            ...details.responseHeaders,
            'X-Content-Type-Options': ['nosniff'],
            'X-Frame-Options': ['DENY'],
            'X-XSS-Protection': ['1; mode=block'],
            'Referrer-Policy': ['strict-origin-when-cross-origin'],
            'Permissions-Policy': ['geolocation=(), microphone=(), camera=()'],
          },
        });
      });
    }

    // Block insecure content
    electronSession.webRequest.onBeforeRequest((details, callback) => {
      const url = new URL(details.url);

      // Block non-HTTPS requests in production (except localhost)
      if (!securityConfig.isProduction() &&
          url.protocol === 'http:' &&
          !url.hostname.includes('localhost')) {
        console.warn(`🚫 Blocked insecure request: ${details.url}`);
        callback({ cancel: true });
        return;
      }

      callback({ cancel: false });
    });
  }

  private buildContentSecurityPolicy(): string {
    const config = securityConfig.getSecurityConfig();

    const cspDirectives = [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline'", // Electron requires unsafe-inline
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
      "style-src-elem 'self' 'unsafe-inline' https://fonts.googleapis.com",
      "img-src 'self' data: https:",
      "font-src 'self' https://fonts.gstatic.com data:",
      "connect-src 'self' https://api.motherduck.com https://fonts.googleapis.com https://fonts.gstatic.com",
      "object-src 'none'",
      "base-uri 'self'",
      "form-action 'self'",
      "frame-ancestors 'none'",
    ];

    // Add development-specific directives
    if (securityConfig.isDevelopment()) {
      cspDirectives[6] = "connect-src 'self' https://api.motherduck.com https://fonts.googleapis.com https://fonts.gstatic.com ws://localhost:* http://localhost:*";
    }

    return cspDirectives.join('; ');
  }

  private async initializeSecurityServices(): Promise<void> {
    // Security services are already initialized as singletons
    // Just verify they're working
    
    // Test input validator
    try {
      inputValidator.validate(inputValidator.schemas.username, 'testuser');
    } catch (error) {
      throw new Error('Input validator initialization failed');
    }

    // Test rate limiter
    try {
      await rateLimiter.checkLimit('test', 'test');
    } catch (error) {
      // Expected for rate limiting, just verify it's working
    }

    console.log('✅ All security services are operational');
  }

  private async setupSecurityMonitoring(): Promise<void> {
    const config = securityConfig.getSecurityConfig();

    // Set up periodic security health checks
    setInterval(async () => {
      await this.performSecurityHealthCheck();
    }, 5 * 60 * 1000); // Every 5 minutes

    // Set up session monitoring
    setInterval(async () => {
      await this.monitorSessions();
    }, 2 * 60 * 1000); // Every 2 minutes

    // Set up intrusion detection monitoring
    setInterval(async () => {
      await this.monitorSuspiciousActivity();
    }, 1 * 60 * 1000); // Every minute
  }

  private async performSecurityHealthCheck(): Promise<void> {
    try {
      const healthCheck = ipcSecurity.performSecurityHealthCheck();
      
      if (healthCheck.status !== 'HEALTHY') {
        await auditLogger.log({
          type: AuditEventType.SECURITY_VIOLATION,
          severity: healthCheck.status === 'CRITICAL' ? AuditSeverity.CRITICAL : AuditSeverity.MEDIUM,
          success: false,
          details: {
            healthCheckStatus: healthCheck.status,
            issues: healthCheck.issues,
            recommendations: healthCheck.recommendations,
          },
        });
      }
    } catch (error) {
      console.error('Security health check failed:', error);
    }
  }

  private async monitorSessions(): Promise<void> {
    try {
      const suspiciousUsers = sessionSecurity.detectSuspiciousSessionActivity();
      
      for (const suspicious of suspiciousUsers) {
        await auditLogger.logSecurityEvent(
          AuditEventType.SUSPICIOUS_ACTIVITY,
          AuditSeverity.MEDIUM,
          {
            suspiciousActivity: suspicious.suspiciousActivity,
            sessionCount: suspicious.sessionCount,
          },
          suspicious.userId
        );
      }
    } catch (error) {
      console.error('Session monitoring failed:', error);
    }
  }

  private async monitorSuspiciousActivity(): Promise<void> {
    try {
      const activeIdentifiers = intrusionDetection.getActiveIdentifiers();
      
      if (activeIdentifiers.length > 10) { // Configurable threshold
        await auditLogger.log({
          type: AuditEventType.SUSPICIOUS_ACTIVITY,
          severity: AuditSeverity.MEDIUM,
          success: false,
          details: {
            highSuspiciousActivityCount: activeIdentifiers.length,
            activeIdentifiers: activeIdentifiers.slice(0, 5), // Log first 5
          },
        });
      }
    } catch (error) {
      console.error('Suspicious activity monitoring failed:', error);
    }
  }

  public async shutdown(): Promise<void> {
    if (!this.isInitialized) {
      return;
    }

    try {
      console.log('🔒 Shutting down Security Manager...');

      // Log shutdown
      await auditLogger.log({
        type: AuditEventType.APPLICATION_STOP,
        severity: AuditSeverity.LOW,
        success: true,
        details: {
          securityShutdown: true,
          timestamp: Date.now(),
        },
      });

      // Cleanup security services
      auditLogger.destroy();
      intrusionDetection.destroy();
      sessionSecurity.destroy();
      rateLimiter.destroy();

      this.isInitialized = false;
      console.log('🔒 Security Manager shutdown complete');

    } catch (error) {
      console.error('❌ Error during security shutdown:', error);
    }
  }

  // Public methods for security status
  public getSecurityStatus(): {
    initialized: boolean;
    productionSecure: boolean;
    activeThreats: number;
    activeSessions: number;
  } {
    return {
      initialized: this.isInitialized,
      productionSecure: securityConfig.isProductionSecure(),
      activeThreats: intrusionDetection.getActiveIdentifiers().length,
      activeSessions: sessionSecurity.getActiveSessions().length,
    };
  }

  public createSecureBrowserWindow(options: Electron.BrowserWindowConstructorOptions = {}): BrowserWindow {
    const config = securityConfig.getSecurityConfig();

    const secureOptions: Electron.BrowserWindowConstructorOptions = {
      ...options,
      webPreferences: {
        ...options.webPreferences,
        nodeIntegration: !config.disableNodeIntegration,
        contextIsolation: config.enableContextIsolation,
        sandbox: config.enableSandbox,
        webSecurity: true,
        allowRunningInsecureContent: false,
        experimentalFeatures: false,
      },
    };

    return new BrowserWindow(secureOptions);
  }

  private async initializeProductionSecurity(): Promise<void> {
    const config = securityConfig.getSecurityConfig();

    if (!securityConfig.isDevelopment()) {
      // Start runtime security monitoring
      runtimeSecurityMonitor.startMonitoring();

      // Initialize secure updater
      if (config.enableAutoUpdates) {
        await secureUpdater.checkForUpdates();
      }

      // Start automatic secure backups
      if (process.env.DATABASE_BACKUP_ENABLED === 'true') {
        secureBackupManager.startAutomaticBackups();
      }

      // Log production security initialization
      await auditLogger.log({
        type: AuditEventType.APPLICATION_START,
        severity: AuditSeverity.LOW,
        success: true,
        details: {
          productionSecurityEnabled: true,
          runtimeMonitoring: true,
          secureUpdates: config.enableAutoUpdates,
          automaticBackups: process.env.DATABASE_BACKUP_ENABLED === 'true',
        },
      });
    }
  }

  public async createSecureWindow(options: Electron.BrowserWindowConstructorOptions): Promise<BrowserWindow> {
    const window = this.createSecureBrowserWindow(options);

    // Apply security headers
    securityHeaders.applyHeaders(window);

    return window;
  }
}

// Export singleton instance
export const securityManager = SecurityManager.getInstance();

// Export all security services for convenience
export {
  securityConfig,
  ipcSecurity,
  auditLogger,
  AuditEventType,
  AuditSeverity,
  intrusionDetection,
  sessionSecurity,
  runtimeSecurityMonitor,
  secureUpdater,
  secureBackupManager,
  certificatePinning,
  securityHeaders,
  rateLimiter,
  inputValidator,
};
