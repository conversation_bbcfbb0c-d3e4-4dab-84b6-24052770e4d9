{"id":"megk41cd-995447zk32n","timestamp":"2025-08-18T03:30:02.317Z","type":"APPLICATION_START","severity":"CRITICAL","ipAddress":"localhost","details":{"securityInitializationFailed":true},"success":false,"errorMessage":"Session can only be received when app is ready","metadata":{}}
{"id":"megk41ce-p5wc2u4sfq8","timestamp":"2025-08-18T03:30:02.318Z","type":"IPC_ERROR","severity":"CRITICAL","ipAddress":"localhost","details":{"stack":"Error: Attempted to register a second handler for 'system:getInfo'\n    at IpcMainImpl.handle (node:electron/js2c/browser_init:2:103687)\n    at ModernTodoApp.setupLegacyHandlers (/home/<USER>/Documents/todo/dist/main/main/index.js:414:28)\n    at ModernTodoApp.setupIpcHandlers (/home/<USER>/Documents/todo/dist/main/main/index.js:243:14)\n    at new ModernTodoApp (/home/<USER>/Documents/todo/dist/main/main/index.js:56:14)\n    at Object.<anonymous> (/home/<USER>/Documents/todo/dist/main/main/index.js:655:1)\n    at Module._compile (node:internal/modules/cjs/loader:1484:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1564:10)\n    at Module.load (node:internal/modules/cjs/loader:1295:32)\n    at Module._load (node:internal/modules/cjs/loader:1111:12)\n    at c._load (node:electron/js2c/node_init:2:16955)","uncaught":true},"success":false,"errorMessage":"Attempted to register a second handler for 'system:getInfo'","metadata":{}}
{"id":"megk41js-efa0re30wuv","timestamp":"2025-08-18T03:30:02.584Z","type":"APPLICATION_START","severity":"CRITICAL","ipAddress":"localhost","details":{"initializationFailed":true},"success":false,"errorMessage":"Session can only be received when app is ready","metadata":{}}
{"id":"megow38a-ctdstbx22n8","timestamp":"2025-08-18T05:43:49.594Z","type":"APPLICATION_START","severity":"CRITICAL","ipAddress":"localhost","details":{"securityInitializationFailed":true},"success":false,"errorMessage":"Session can only be received when app is ready","metadata":{}}
{"id":"megow38a-e8avzushwuw","timestamp":"2025-08-18T05:43:49.594Z","type":"IPC_ERROR","severity":"CRITICAL","ipAddress":"localhost","details":{"stack":"Error: Attempted to register a second handler for 'system:getInfo'\n    at IpcMainImpl.handle (node:electron/js2c/browser_init:2:103687)\n    at ModernTodoApp.setupLegacyHandlers (/home/<USER>/Documents/todo/dist/main/main/index.js:414:28)\n    at ModernTodoApp.setupIpcHandlers (/home/<USER>/Documents/todo/dist/main/main/index.js:243:14)\n    at new ModernTodoApp (/home/<USER>/Documents/todo/dist/main/main/index.js:56:14)\n    at Object.<anonymous> (/home/<USER>/Documents/todo/dist/main/main/index.js:655:1)\n    at Module._compile (node:internal/modules/cjs/loader:1484:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1564:10)\n    at Module.load (node:internal/modules/cjs/loader:1295:32)\n    at Module._load (node:internal/modules/cjs/loader:1111:12)\n    at c._load (node:electron/js2c/node_init:2:16955)","uncaught":true},"success":false,"errorMessage":"Attempted to register a second handler for 'system:getInfo'","metadata":{}}
{"id":"megow3fk-xh32vbyz2kl","timestamp":"2025-08-18T05:43:49.856Z","type":"APPLICATION_START","severity":"CRITICAL","ipAddress":"localhost","details":{"initializationFailed":true},"success":false,"errorMessage":"Session can only be received when app is ready","metadata":{}}
